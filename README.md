# Employment Ontario Automation Framework

This is a Selenium Java automation framework with Page Object Model (POM) design pattern for automating the Employment Ontario application.

## Framework Structure

```
src/main/java/mto/ltc/eo
├── constants
│   └── AppConstants.java
├── factory
│   ├── DriverFactory.java
│   └── OptionsManager.java
├── pages
│   ├── BasePage.java
│   ├── ClientInfoPage.java (Step 1 of 3)
│   ├── LocationSelectionPage.java (Step 2 of 3)
│   ├── ConfirmationPage.java (Step 3 of 3)
│   └── SuccessPage.java (Final confirmation page)
├── util
│   ├── ElementUtil.java
│   ├── JavaScriptUtil.java
│   ├── ExtentReportManager.java
│   ├── TestListener.java
│   ├── ReportManager.java
│   └── LoggerUtil.java
└── exception
    └── FrameworkException.java

src/test/java/mto/ltc/eo
├── base
│   └── BaseTest.java
└── tests
    ├── ClientInfoPageTest.java
    ├── LocationSelectionPageTest.java
    ├── ConfirmationPageTest.java
    ├── SuccessPageTest.java
    ├── HeaderTest.java
    └── ESIProgramTest.java

src/test/resources
├── config
│   └── config.properties
├── testrunners
│   └── testng.xml
└── logback.xml
```

## Features

- Page Object Model design pattern
- ThreadLocal WebDriver for thread safety
- Configurable browser options
- Utility classes for common operations
- TestNG for test execution
- **Comprehensive Reporting**:
  - ExtentReports with detailed HTML reports
  - Beautiful HTML reports with dark theme
  - Screenshots embedded in reports
- **Advanced Logging**:
  - SLF4J with Logback for structured logging
  - Separate log files for different categories
  - Test results logging
  - Browser actions logging
  - Element interactions logging
- **Screenshot Management**:
  - Automatic screenshot capture on test failure
  - Optional screenshot capture on test success
  - Screenshots embedded in reports
- **Enhanced Test Tracking**:
  - Detailed step-by-step logging
  - Test execution timeline
  - Performance metrics
- Configurable test data

## Test Cases

1. **Header Tests**
   - Test page title
   - Test Ontario logo is displayed
   - Test language toggle is displayed and set to French
   - Test ministry header is displayed
   - Test "Find an Employment Counsellor" header is displayed

2. **ESI Program Tests**
   - Test ESI program text is displayed
   - Test "click here" link is displayed
   - Test "click here" link opens correct URL in new tab

3. **Client Info Page Tests**
   - Test form submission

4. **Location Selection Page Tests**
   - Test location selection page header
   - Test locations are available
   - Test location selection and navigation to confirmation page

5. **Confirmation Page Tests**
   - Test confirmation page header
   - Test location details are displayed
   - Test back button navigation
   - Test confirm and submit button navigation

6. **Success Page Tests**
   - Test success message is displayed
   - Test check circle icon is displayed
   - Test confirmation text is displayed
   - Test application ID is displayed
   - Test email confirmation is displayed
   - Test service language is displayed
   - Test location text is displayed

## Logging and Reporting

### Logging
The framework uses SLF4J with Logback for comprehensive logging:

- **Main Log File**: `logs/employment-ontario-automation.log` - Contains all framework logs
- **Test Results Log**: `logs/test-results.log` - Contains only test execution results
- **Browser Actions Log**: `logs/browser-actions.log` - Contains browser and element interactions

### Reporting
The framework provides comprehensive reporting with ExtentReports:

#### ExtentReports
- **Location**: `test-output/extent-reports/extent-report.html`
- **Features**:
  - Beautiful HTML reports with dark theme
  - Detailed test execution logs
  - Screenshots for failures and successes
  - Test execution timeline
  - System information
  - Test categorization and filtering
  - Performance metrics

### Log Levels and Categories
- **STEP**: Test step descriptions
- **INFO**: General information and test data
- **WARN**: Warnings and skipped tests
- **ERROR**: Failures and exceptions
- **BROWSER ACTION**: Browser-specific actions
- **ELEMENT INTERACTION**: Element interactions

## How to Run

1. Clone the repository
2. Configure `config.properties` file if needed
3. Run the tests using Maven:
   ```
   mvn clean test
   ```
4. Or run the TestNG XML file directly from your IDE
5. **View Reports and Logs**:
   - **ExtentReports**: Open `test-output/extent-reports/extent-report.html` in a browser
   - **Logs**: Check the `logs/` directory for detailed execution logs

## Configuration

### Logging Configuration
Edit `src/test/resources/logback.xml` to customize:
- Log levels
- Log file locations
- Log rotation policies
- Log formats

## Requirements

- Java 17 or higher
- Maven 3.8.0 or higher
- Chrome/Firefox/Edge browser installed
