# Employment Ontario Automation Framework

This is a Selenium Java automation framework with Page Object Model (POM) design pattern for automating the Employment Ontario application.

## Framework Structure

```
src/main/java/mto/ltc/eo
├── constants
│   └── AppConstants.java
├── factory
│   ├── DriverFactory.java
│   └── OptionsManager.java
├── pages
│   ├── BasePage.java
│   ├── ClientInfoPage.java (Step 1 of 3)
│   ├── LocationSelectionPage.java (Step 2 of 3)
│   ├── ConfirmationPage.java (Step 3 of 3)
│   └── SuccessPage.java (Final confirmation page)
├── util
│   ├── ElementUtil.java
│   └── JavaScriptUtil.java
└── exception
    └── FrameworkException.java

src/test/java/mto/ltc/eo
├── base
│   └── BaseTest.java
└── tests
    ├── ClientInfoPageTest.java
    ├── LocationSelectionPageTest.java
    ├── ConfirmationPageTest.java
    ├── SuccessPageTest.java
    ├── HeaderTest.java
    └── ESIProgramTest.java

src/test/resources
├── config
│   └── config.properties
└── testrunners
    └── testng.xml
```

## Features

- Page Object Model design pattern
- ThreadLocal WebDriver for thread safety
- Configurable browser options
- Utility classes for common operations
- TestNG for test execution
- Proper reporting with ExtentReports
- Screenshot capture on test failure
- Configurable test data

## Test Cases

1. **Header Tests**
   - Test page title
   - Test Ontario logo is displayed
   - Test language toggle is displayed and set to French
   - Test ministry header is displayed
   - Test "Find an Employment Counsellor" header is displayed

2. **ESI Program Tests**
   - Test ESI program text is displayed
   - Test "click here" link is displayed
   - Test "click here" link opens correct URL in new tab

3. **Client Info Page Tests**
   - Test form submission

4. **Location Selection Page Tests**
   - Test location selection page header
   - Test locations are available
   - Test location selection and navigation to confirmation page

5. **Confirmation Page Tests**
   - Test confirmation page header
   - Test location details are displayed
   - Test back button navigation
   - Test confirm and submit button navigation

6. **Success Page Tests**
   - Test success message is displayed
   - Test check circle icon is displayed
   - Test confirmation text is displayed
   - Test application ID is displayed
   - Test email confirmation is displayed
   - Test service language is displayed
   - Test location text is displayed

## How to Run

1. Clone the repository
2. Configure `config.properties` file if needed
3. Run the tests using Maven:
   ```
   mvn clean test
   ```
4. Or run the TestNG XML file directly from your IDE

## Requirements

- Java 17 or higher
- Maven 3.8.0 or higher
- Chrome/Firefox/Edge browser installed
