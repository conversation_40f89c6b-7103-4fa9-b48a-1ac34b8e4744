package com.qa.eo.base;

import java.util.Properties;

import org.openqa.selenium.WebDriver;
import org.testng.annotations.AfterTest;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Parameters;

import com.qa.eo.factory.DriverFactory;
import com.qa.eo.pages.BasePage;
import com.qa.eo.pages.ClientInfoPage;
import com.qa.eo.pages.ConfirmationPage;
import com.qa.eo.pages.LocationSelectionPage;
import com.qa.eo.pages.SuccessPage;

/**
 * Base test class with common test setup and teardown
 */
public class BaseTest {

    protected WebDriver driver;
    protected Properties prop;
    protected DriverFactory df;
    
    // Page objects
    protected BasePage basePage;
    protected ClientInfoPage clientInfoPage;
    protected LocationSelectionPage locationSelectionPage;
    protected ConfirmationPage confirmationPage;
    protected SuccessPage successPage;
    
    @Parameters({"browser", "browserversion"})
    @BeforeTest
    public void setup(String browserName, String browserVersion) {
        df = new DriverFactory();
        prop = df.initProp();
        
        if (browserName != null) {
            prop.setProperty("browser", browserName);
            prop.setProperty("browserversion", browserVersion);
        }
        
        driver = df.initDriver(prop);
        
        // Initialize page objects
        basePage = new BasePage(driver);
        clientInfoPage = new ClientInfoPage(driver);
    }
    
    @AfterTest
    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }
}
