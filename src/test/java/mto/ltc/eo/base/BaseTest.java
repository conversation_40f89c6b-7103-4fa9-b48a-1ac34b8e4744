package mto.ltc.eo.base;

import java.util.Properties;

import com.aventstack.chaintest.ChainTest;
import com.aventstack.chaintest.annotation.ChainTestClass;
import com.aventstack.chaintest.annotation.ChainTestMethod;
import org.openqa.selenium.WebDriver;
import org.testng.ITestResult;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.AfterTest;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Parameters;

import mto.ltc.eo.factory.DriverFactory;
import mto.ltc.eo.pages.BasePage;
import mto.ltc.eo.pages.ClientInfoPage;
import mto.ltc.eo.pages.ConfirmationPage;
import mto.ltc.eo.pages.LocationSelectionPage;
import mto.ltc.eo.pages.SuccessPage;
import mto.ltc.eo.util.ChainTestManager;
import mto.ltc.eo.util.ElementUtil;

/**
 * Base test class with common test setup and teardown
 */
public class BaseTest {

    protected WebDriver driver;
    protected Properties prop;
    protected DriverFactory df;
    protected ElementUtil elementUtil;

    // Page objects
    protected BasePage basePage;
    protected ClientInfoPage clientInfoPage;
    protected LocationSelectionPage locationSelectionPage;
    protected ConfirmationPage confirmationPage;
    protected SuccessPage successPage;

    @BeforeTest
    @Parameters(value = {"browser", "browserversion"})
    public void setup(@org.testng.annotations.Optional String browserName, @org.testng.annotations.Optional String browserVersion) {
        df = new DriverFactory();
        prop = df.initProp();

        if (browserName != null) {
            prop.setProperty("browser", browserName);
            prop.setProperty("browserversion", browserVersion);
        }

        driver = df.initDriver(prop);

        // Initialize utilities
        elementUtil = new ElementUtil(driver);

        // Initialize page objects
        basePage = new BasePage(driver);
        clientInfoPage = new ClientInfoPage(driver);
    }

    @AfterTest
    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }
}
