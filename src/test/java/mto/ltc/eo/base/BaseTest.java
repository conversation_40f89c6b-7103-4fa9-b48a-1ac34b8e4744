package mto.ltc.eo.base;

import java.util.Properties;

import org.openqa.selenium.WebDriver;
import org.testng.annotations.AfterTest;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Parameters;

import mto.ltc.eo.factory.DriverFactory;
import mto.ltc.eo.pages.BasePage;
import mto.ltc.eo.pages.ClientInfoPage;
import mto.ltc.eo.pages.ConfirmationPage;
import mto.ltc.eo.pages.LocationSelectionPage;
import mto.ltc.eo.pages.SuccessPage;
import mto.ltc.eo.util.ElementUtil;
import mto.ltc.eo.util.LoggerUtil;
import mto.ltc.eo.util.ReportManager;

/**
 * Base test class with common test setup and teardown
 */
public class BaseTest {

    protected WebDriver driver;
    protected Properties prop;
    protected DriverFactory df;
    protected ElementUtil elementUtil;

    // Page objects
    protected BasePage basePage;
    protected ClientInfoPage clientInfoPage;
    protected LocationSelectionPage locationSelectionPage;
    protected ConfirmationPage confirmationPage;
    protected SuccessPage successPage;

    @BeforeTest
    @Parameters(value = {"browser", "browserversion"})
    public void setup(@org.testng.annotations.Optional String browserName, @org.testng.annotations.Optional String browserVersion) {
        LoggerUtil.info("=== Starting Test Setup ===");

        df = new DriverFactory();
        prop = df.initProp();
        LoggerUtil.info("Configuration properties loaded successfully");

        if (browserName != null && !browserName.isEmpty()) {
            prop.setProperty("browser", browserName);
            prop.setProperty("browserversion", browserVersion);
            LoggerUtil.info("Browser overridden from TestNG XML: " + browserName + " (" + browserVersion + ")");
        } else {
            LoggerUtil.info("Using browser from config.properties: " + prop.getProperty("browser"));
        }

        driver = df.initDriver(prop);
        LoggerUtil.logBrowserAction("Browser initialized: " + prop.getProperty("browser"));
        LoggerUtil.logBrowserAction("Navigated to URL: " + prop.getProperty("url"));

        // Initialize utilities
        elementUtil = new ElementUtil(driver);
        LoggerUtil.info("ElementUtil initialized successfully");

        // Initialize page objects
        basePage = new BasePage(driver);
        clientInfoPage = new ClientInfoPage(driver);
        LoggerUtil.info("Page objects initialized successfully");

        LoggerUtil.info("=== Test Setup Completed ===");
    }

    @AfterTest
    public void tearDown() {
        LoggerUtil.info("=== Starting Test Teardown ===");

        if (driver != null) {
            LoggerUtil.logBrowserAction("Closing browser");
            driver.quit();
            LoggerUtil.info("Browser closed successfully");
        } else {
            LoggerUtil.warn("Driver was null during teardown");
        }

        LoggerUtil.info("=== Test Teardown Completed ===");
    }
}
