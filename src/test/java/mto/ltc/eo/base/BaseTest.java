package mto.ltc.eo.base;

import java.util.Properties;

import org.openqa.selenium.WebDriver;
import org.testng.annotations.AfterTest;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Parameters;

import mto.ltc.eo.factory.DriverFactory;
import mto.ltc.eo.pages.BasePage;
import mto.ltc.eo.pages.ClientInfoPage;
import mto.ltc.eo.pages.ConfirmationPage;
import mto.ltc.eo.pages.LocationSelectionPage;
import mto.ltc.eo.pages.SuccessPage;
import mto.ltc.eo.util.ElementUtil;

/**
 * Base test class with common test setup and teardown
 */
public class BaseTest {

    protected WebDriver driver;
    protected Properties prop;
    protected DriverFactory df;
    protected ElementUtil elementUtil;

    // Page objects
    protected BasePage basePage;
    protected ClientInfoPage clientInfoPage;
    protected LocationSelectionPage locationSelectionPage;
    protected ConfirmationPage confirmationPage;
    protected SuccessPage successPage;

    @BeforeTest
    @Parameters(value = {"browser", "browserversion"}, alwaysRun = true)
    public void setup(@org.testng.annotations.Optional String browserName, @org.testng.annotations.Optional String browserVersion) {
        // Parameters are optional in TestNG XML
        df = new DriverFactory();
        prop = df.initProp();

        // Only override if parameters are provided and not null or empty
        if (browserName != null && !browserName.isEmpty()) {
            System.out.println("Using browser from TestNG XML: " + browserName);
            prop.setProperty("browser", browserName);

            if (browserVersion != null && !browserVersion.isEmpty()) {
                prop.setProperty("browserversion", browserVersion);
            }
        } else {
            System.out.println("Using browser from config.properties: " + prop.getProperty("browser"));
        }

        driver = df.initDriver(prop);

        // Initialize utilities
        elementUtil = new ElementUtil(driver);

        // Initialize page objects
        basePage = new BasePage(driver);
        clientInfoPage = new ClientInfoPage(driver);
    }

    @AfterTest
    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }
}
