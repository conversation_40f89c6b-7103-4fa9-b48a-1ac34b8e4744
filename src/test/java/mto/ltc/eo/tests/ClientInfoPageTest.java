package com.qa.eo.tests;

import org.testng.Assert;
import org.testng.annotations.Test;

import com.qa.eo.base.BaseTest;
import com.qa.eo.constants.AppConstants;

/**
 * Test class for Client Information page
 */
public class ClientInfoPageTest extends BaseTest {

    @Test(priority = 1, description = "Test ESI program text is displayed")
    public void testESIProgramTextTest() {
        Assert.assertTrue(clientInfoPage.isESIProgramTextDisplayed(), "ESI program text is not displayed");
        String programText = clientInfoPage.getESIProgramText();
        Assert.assertTrue(programText.contains(AppConstants.ESI_PROGRAM_TEXT), "ESI program text is not correct");
    }
    
    @Test(priority = 2, description = "Test 'click here' link is displayed")
    public void testClickHereLinkTest() {
        Assert.assertTrue(clientInfoPage.isClickHereLinkDisplayed(), "Click here link is not displayed");
    }
    
    @Test(priority = 3, description = "Test form submission")
    public void testFormSubmissionTest() {
        locationSelectionPage = clientInfoPage.fillClientInfoForm(
            "John", // firstName
            "M", // middleName
            "Doe", // lastName
            "Johnny", // preferredFirstName
            "", // preferredLastName
            "123 Main St", // address
            "Toronto", // city
            "M1M1P2", // postalCode
            "<EMAIL>", // email
            "4161234567", // phone
            "English" // preferredLanguage
        );
        
        Assert.assertTrue(locationSelectionPage.isPageHeaderDisplayed(), "Location selection page is not displayed after form submission");
        String headerText = locationSelectionPage.getPageHeaderText();
        Assert.assertEquals(headerText, "Select a location", "Location selection page header is not correct");
    }
}
