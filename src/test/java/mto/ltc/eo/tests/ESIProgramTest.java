package mto.ltc.eo.tests;

import org.testng.Assert;
import org.testng.annotations.Test;

import mto.ltc.eo.base.BaseTest;
import mto.ltc.eo.constants.AppConstants;

/**
 * Test class for ESI Program specific features
 */
public class ESIProgramTest extends BaseTest {

    @Test(priority = 1, description = "Test ESI program text is displayed")
    public void testESIProgramTextTest() {
        Assert.assertTrue(clientInfoPage.isESIProgramTextDisplayed(), "ESI program text is not displayed");
        String programText = clientInfoPage.getESIProgramText();
        Assert.assertTrue(programText.contains(AppConstants.ESI_PROGRAM_TEXT), "ESI program text is not correct");
    }
    
    @Test(priority = 2, description = "Test 'click here' link is displayed")
    public void testClickHereLinkTest() {
        Assert.assertTrue(clientInfoPage.isClickHereLinkDisplayed(), "Click here link is not displayed");
    }
    
    @Test(priority = 3, description = "Test 'click here' link opens correct URL in new tab")
    public void testClickHereLinkNavigationTest() {
        // Store the current window handle
        String originalWindow = driver.getWindowHandle();
        
        // Click on the "click here" link
        clientInfoPage.clickOnClickHereLink();
        
        // Wait for the new window to open
        elementUtil.waitForNumberOfWindows(2, AppConstants.DEFAULT_MEDIUM_TIME_OUT);
        
        // Switch to the new window
        for (String windowHandle : driver.getWindowHandles()) {
            if (!originalWindow.equals(windowHandle)) {
                driver.switchTo().window(windowHandle);
                break;
            }
        }
        
        // Verify the URL
        String newTabUrl = driver.getCurrentUrl();
        Assert.assertTrue(newTabUrl.contains(AppConstants.BASE_URL), "New tab URL is not correct");
        
        // Close the new tab and switch back to the original window
        driver.close();
        driver.switchTo().window(originalWindow);
    }
}
