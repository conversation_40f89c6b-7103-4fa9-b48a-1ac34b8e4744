package mto.ltc.eo.tests;

import org.testng.Assert;
import org.testng.annotations.Test;

import mto.ltc.eo.base.BaseTest;
import mto.ltc.eo.constants.AppConstants;
import mto.ltc.eo.util.ChainTestManager;
import mto.ltc.eo.util.TestListener;

/**
 * Test class for header elements
 */
public class HeaderTest extends BaseTest {

    @Test(priority = 1, description = "Test page title")
    public void testPageTitleTest() {
        ChainTestManager.logStep("Getting the page title");
        String actualTitle = basePage.getPageTitle();
        ChainTestManager.logInfo("Actual page title: " + actualTitle);
        ChainTestManager.logInfo("Expected page title: " + AppConstants.HOME_PAGE_TITLE);

        ChainTestManager.logStep("Verifying the page title matches expected value");
        Assert.assertEquals(actualTitle, AppConstants.HOME_PAGE_TITLE, "Page title is not correct");

        ChainTestManager.logPass("Page title verification completed successfully");
        ChainTestManager.takeScreenshot(driver, "Page Title Verification");
    }

    @Test(priority = 2, description = "Test Ontario logo is displayed")
    public void testOntarioLogoTest() {
        ChainTestManager.logStep("Checking if Ontario logo is displayed on the page");
        boolean isLogoDisplayed = basePage.isOntarioLogoDisplayed();
        ChainTestManager.logInfo("Ontario logo displayed: " + isLogoDisplayed);

        ChainTestManager.logStep("Verifying Ontario logo is visible");
        Assert.assertTrue(isLogoDisplayed, "Ontario logo is not displayed");

        ChainTestManager.logPass("Ontario logo verification completed successfully");
        ChainTestManager.takeScreenshot(driver, "Ontario Logo Verification");
    }

    @Test(priority = 3, description = "Test language toggle is displayed and set to French")
    public void testLanguageToggleTest() {
        ChainTestManager.logStep("Checking if language toggle is displayed on the page");
        boolean isToggleDisplayed = basePage.isLanguageToggleDisplayed();
        ChainTestManager.logInfo("Language toggle displayed: " + isToggleDisplayed);
        Assert.assertTrue(isToggleDisplayed, "Language toggle is not displayed");

        ChainTestManager.logStep("Getting language toggle text");
        String toggleText = basePage.getLanguageToggleText();
        ChainTestManager.logInfo("Actual toggle text: " + toggleText);
        ChainTestManager.logInfo("Expected toggle text: Français");

        ChainTestManager.logStep("Verifying language toggle text is set to French");
        Assert.assertEquals(toggleText, "Français", "Language toggle text is not correct");

        ChainTestManager.logPass("Language toggle verification completed successfully");
        ChainTestManager.takeScreenshot(driver, "Language Toggle Verification");
    }

    @Test(priority = 4, description = "Test ministry header is displayed")
    public void testMinistryHeaderTest() {
        ChainTestManager.logStep("Checking if ministry header is displayed on the page");
        boolean isHeaderDisplayed = basePage.isMinistryHeaderDisplayed();
        ChainTestManager.logInfo("Ministry header displayed: " + isHeaderDisplayed);
        Assert.assertTrue(isHeaderDisplayed, "Ministry header is not displayed");

        ChainTestManager.logStep("Getting ministry header text");
        String headerText = basePage.getMinistryHeaderText();
        ChainTestManager.logInfo("Actual header text: " + headerText);
        ChainTestManager.logInfo("Expected header text: " + AppConstants.MINISTRY_HEADER);

        ChainTestManager.logStep("Verifying ministry header text matches expected value");
        Assert.assertEquals(headerText, AppConstants.MINISTRY_HEADER, "Ministry header text is not correct");

        ChainTestManager.logPass("Ministry header verification completed successfully");
        ChainTestManager.takeScreenshot(driver, "Ministry Header Verification");
    }

    @Test(priority = 5, description = "Test 'Find an Employment Counsellor' header is displayed")
    public void testFindCounsellorHeaderTest() {
        ChainTestManager.logStep("Checking if 'Find an Employment Counsellor' header is displayed");
        boolean isHeaderDisplayed = basePage.isFindCounsellorHeaderDisplayed();
        ChainTestManager.logInfo("Find Counsellor header displayed: " + isHeaderDisplayed);
        Assert.assertTrue(isHeaderDisplayed, "Find Counsellor header is not displayed");

        ChainTestManager.logStep("Getting 'Find an Employment Counsellor' header text");
        String headerText = basePage.getFindCounsellorHeaderText();
        ChainTestManager.logInfo("Actual header text: " + headerText);
        ChainTestManager.logInfo("Expected header text: " + AppConstants.FIND_COUNSELLOR_HEADER);

        ChainTestManager.logStep("Verifying 'Find an Employment Counsellor' header text");
        Assert.assertEquals(headerText, AppConstants.FIND_COUNSELLOR_HEADER, "Find Counsellor header text is not correct");

        ChainTestManager.logPass("Find Counsellor header verification completed successfully");
        ChainTestManager.takeScreenshot(driver, "Find Counsellor Header Verification");
    }
}
