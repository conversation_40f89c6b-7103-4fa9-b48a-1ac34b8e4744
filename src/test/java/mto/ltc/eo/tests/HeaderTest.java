package mto.ltc.eo.tests;

import org.testng.Assert;
import org.testng.annotations.Test;

import mto.ltc.eo.base.BaseTest;
import mto.ltc.eo.constants.AppConstants;

/**
 * Test class for header elements
 */
public class HeaderTest extends BaseTest {

    @Test(priority = 1, description = "Test page title")
    public void testPageTitleTest() {
        String actualTitle = basePage.getPageTitle();
        Assert.assertEquals(actualTitle, AppConstants.HOME_PAGE_TITLE, "Page title is not correct");
    }
    
    @Test(priority = 2, description = "Test Ontario logo is displayed")
    public void testOntarioLogoTest() {
        Assert.assertTrue(basePage.isOntarioLogoDisplayed(), "Ontario logo is not displayed");
    }
    
    @Test(priority = 3, description = "Test language toggle is displayed and set to French")
    public void testLanguageToggleTest() {
        Assert.assertTrue(basePage.isLanguageToggleDisplayed(), "Language toggle is not displayed");
        String toggleText = basePage.getLanguageToggleText();
        Assert.assertEquals(toggleText, "Français", "Language toggle text is not correct");
    }
    
    @Test(priority = 4, description = "Test ministry header is displayed")
    public void testMinistryHeaderTest() {
        Assert.assertTrue(basePage.isMinistryHeaderDisplayed(), "Ministry header is not displayed");
        String headerText = basePage.getMinistryHeaderText();
        Assert.assertEquals(headerText, AppConstants.MINISTRY_HEADER, "Ministry header text is not correct");
    }
    
    @Test(priority = 5, description = "Test 'Find an Employment Counsellor' header is displayed")
    public void testFindCounsellorHeaderTest() {
        Assert.assertTrue(basePage.isFindCounsellorHeaderDisplayed(), "Find Counsellor header is not displayed");
        String headerText = basePage.getFindCounsellorHeaderText();
        Assert.assertEquals(headerText, AppConstants.FIND_COUNSELLOR_HEADER, "Find Counsellor header text is not correct");
    }
}
