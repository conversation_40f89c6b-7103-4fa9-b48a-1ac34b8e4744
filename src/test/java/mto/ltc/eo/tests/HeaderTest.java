package mto.ltc.eo.tests;

import org.testng.Assert;
import org.testng.annotations.Test;

import mto.ltc.eo.base.BaseTest;
import mto.ltc.eo.constants.AppConstants;
import mto.ltc.eo.util.ReportManager;
import mto.ltc.eo.util.TestListener;

/**
 * Test class for header elements
 */
public class HeaderTest extends BaseTest {

    @Test(priority = 1, description = "Test page title")
    public void testPageTitleTest() {
        ReportManager.logStep("Getting the page title");
        String actualTitle = basePage.getPageTitle();
        ReportManager.logInfo("Actual page title: " + actualTitle);
        ReportManager.logInfo("Expected page title: " + AppConstants.HOME_PAGE_TITLE);

        ReportManager.logStep("Verifying the page title matches expected value");
        Assert.assertEquals(actualTitle, AppConstants.HOME_PAGE_TITLE, "Page title is not correct");

        ReportManager.logPass("Page title verification completed successfully");
        ReportManager.takeScreenshot(driver, "Page Title Verification");
    }

    @Test(priority = 2, description = "Test Ontario logo is displayed")
    public void testOntarioLogoTest() {
        ReportManager.logStep("Checking if Ontario logo is displayed on the page");
        boolean isLogoDisplayed = basePage.isOntarioLogoDisplayed();
        ReportManager.logInfo("Ontario logo displayed: " + isLogoDisplayed);

        ReportManager.logStep("Verifying Ontario logo is visible");
        Assert.assertTrue(isLogoDisplayed, "Ontario logo is not displayed");

        ReportManager.logPass("Ontario logo verification completed successfully");
        ReportManager.takeScreenshot(driver, "Ontario Logo Verification");
    }

    @Test(priority = 3, description = "Test language toggle is displayed and set to French")
    public void testLanguageToggleTest() {
        ReportManager.logStep("Checking if language toggle is displayed on the page");
        boolean isToggleDisplayed = basePage.isLanguageToggleDisplayed();
        ReportManager.logInfo("Language toggle displayed: " + isToggleDisplayed);
        Assert.assertTrue(isToggleDisplayed, "Language toggle is not displayed");

        ReportManager.logStep("Getting language toggle text");
        String toggleText = basePage.getLanguageToggleText();
        ReportManager.logInfo("Actual toggle text: " + toggleText);
        ReportManager.logInfo("Expected toggle text: Français");

        ReportManager.logStep("Verifying language toggle text is set to French");
        Assert.assertEquals(toggleText, "Français", "Language toggle text is not correct");

        ReportManager.logPass("Language toggle verification completed successfully");
        ReportManager.takeScreenshot(driver, "Language Toggle Verification");
    }

    @Test(priority = 4, description = "Test ministry header is displayed")
    public void testMinistryHeaderTest() {
        ReportManager.logStep("Checking if ministry header is displayed on the page");
        boolean isHeaderDisplayed = basePage.isMinistryHeaderDisplayed();
        ReportManager.logInfo("Ministry header displayed: " + isHeaderDisplayed);
        Assert.assertTrue(isHeaderDisplayed, "Ministry header is not displayed");

        ReportManager.logStep("Getting ministry header text");
        String headerText = basePage.getMinistryHeaderText();
        ReportManager.logInfo("Actual header text: " + headerText);
        ReportManager.logInfo("Expected header text: " + AppConstants.MINISTRY_HEADER);

        ReportManager.logStep("Verifying ministry header text matches expected value");
        Assert.assertEquals(headerText, AppConstants.MINISTRY_HEADER, "Ministry header text is not correct");

        ReportManager.logPass("Ministry header verification completed successfully");
        ReportManager.takeScreenshot(driver, "Ministry Header Verification");
    }

    @Test(priority = 5, description = "Test 'Find an Employment Counsellor' header is displayed")
    public void testFindCounsellorHeaderTest() {
        ReportManager.logStep("Checking if 'Find an Employment Counsellor' header is displayed");
        boolean isHeaderDisplayed = basePage.isFindCounsellorHeaderDisplayed();
        ReportManager.logInfo("Find Counsellor header displayed: " + isHeaderDisplayed);
        Assert.assertTrue(isHeaderDisplayed, "Find Counsellor header is not displayed");

        ReportManager.logStep("Getting 'Find an Employment Counsellor' header text");
        String headerText = basePage.getFindCounsellorHeaderText();
        ReportManager.logInfo("Actual header text: " + headerText);
        ReportManager.logInfo("Expected header text: " + AppConstants.FIND_COUNSELLOR_HEADER);

        ReportManager.logStep("Verifying 'Find an Employment Counsellor' header text");
        Assert.assertEquals(headerText, AppConstants.FIND_COUNSELLOR_HEADER, "Find Counsellor header text is not correct");

        ReportManager.logPass("Find Counsellor header verification completed successfully");
        ReportManager.takeScreenshot(driver, "Find Counsellor Header Verification");
    }
}
