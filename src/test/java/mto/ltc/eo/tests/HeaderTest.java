package mto.ltc.eo.tests;

import com.aventstack.chaintest.annotation.ChainTestMethod;
import org.testng.Assert;
import org.testng.annotations.Test;

import mto.ltc.eo.base.BaseTest;
import mto.ltc.eo.constants.AppConstants;
import mto.ltc.eo.util.ChainTestManager;

/**
 * Test class for header elements
 */
public class HeaderTest extends BaseTest {

    @Test(priority = 1, description = "Test page title")
    @ChainTestMethod(name = "Page Title Test", description = "Verify that the page title is correct")
    public void testPageTitleTest() {
        ChainTestManager.logStep("Getting the page title");
        String actualTitle = basePage.getPageTitle();
        ChainTestManager.logInfo("Actual page title: " + actualTitle);

        ChainTestManager.logStep("Verifying the page title");
        Assert.assertEquals(actualTitle, AppConstants.HOME_PAGE_TITLE, "Page title is not correct");
        ChainTestManager.takeScreenshot(driver, "Page Title Verification");
    }

    @Test(priority = 2, description = "Test Ontario logo is displayed")
    @ChainTestMethod(name = "Ontario Logo Test", description = "Verify that the Ontario logo is displayed")
    public void testOntarioLogoTest() {
        ChainTestManager.logStep("Checking if Ontario logo is displayed");
        boolean isLogoDisplayed = basePage.isOntarioLogoDisplayed();
        ChainTestManager.logInfo("Ontario logo displayed: " + isLogoDisplayed);

        ChainTestManager.logStep("Verifying Ontario logo is displayed");
        Assert.assertTrue(isLogoDisplayed, "Ontario logo is not displayed");
        ChainTestManager.takeScreenshot(driver, "Ontario Logo Verification");
    }

    @Test(priority = 3, description = "Test language toggle is displayed and set to French")
    @ChainTestMethod(name = "Language Toggle Test", description = "Verify that the language toggle is displayed and set to French")
    public void testLanguageToggleTest() {
        ChainTestManager.logStep("Checking if language toggle is displayed");
        boolean isToggleDisplayed = basePage.isLanguageToggleDisplayed();
        ChainTestManager.logInfo("Language toggle displayed: " + isToggleDisplayed);
        Assert.assertTrue(isToggleDisplayed, "Language toggle is not displayed");

        ChainTestManager.logStep("Getting language toggle text");
        String toggleText = basePage.getLanguageToggleText();
        ChainTestManager.logInfo("Language toggle text: " + toggleText);

        ChainTestManager.logStep("Verifying language toggle text is set to French");
        Assert.assertEquals(toggleText, "Français", "Language toggle text is not correct");
        ChainTestManager.takeScreenshot(driver, "Language Toggle Verification");
    }

    @Test(priority = 4, description = "Test ministry header is displayed")
    @ChainTestMethod(name = "Ministry Header Test", description = "Verify that the ministry header is displayed and has correct text")
    public void testMinistryHeaderTest() {
        ChainTestManager.logStep("Checking if ministry header is displayed");
        boolean isHeaderDisplayed = basePage.isMinistryHeaderDisplayed();
        ChainTestManager.logInfo("Ministry header displayed: " + isHeaderDisplayed);
        Assert.assertTrue(isHeaderDisplayed, "Ministry header is not displayed");

        ChainTestManager.logStep("Getting ministry header text");
        String headerText = basePage.getMinistryHeaderText();
        ChainTestManager.logInfo("Ministry header text: " + headerText);

        ChainTestManager.logStep("Verifying ministry header text");
        Assert.assertEquals(headerText, AppConstants.MINISTRY_HEADER, "Ministry header text is not correct");
        ChainTestManager.takeScreenshot(driver, "Ministry Header Verification");
    }

    @Test(priority = 5, description = "Test 'Find an Employment Counsellor' header is displayed")
    @ChainTestMethod(name = "Find Counsellor Header Test", description = "Verify that the 'Find an Employment Counsellor' header is displayed and has correct text")
    public void testFindCounsellorHeaderTest() {
        ChainTestManager.logStep("Checking if 'Find an Employment Counsellor' header is displayed");
        boolean isHeaderDisplayed = basePage.isFindCounsellorHeaderDisplayed();
        ChainTestManager.logInfo("Find Counsellor header displayed: " + isHeaderDisplayed);
        Assert.assertTrue(isHeaderDisplayed, "Find Counsellor header is not displayed");

        ChainTestManager.logStep("Getting 'Find an Employment Counsellor' header text");
        String headerText = basePage.getFindCounsellorHeaderText();
        ChainTestManager.logInfo("Find Counsellor header text: " + headerText);

        ChainTestManager.logStep("Verifying 'Find an Employment Counsellor' header text");
        Assert.assertEquals(headerText, AppConstants.FIND_COUNSELLOR_HEADER, "Find Counsellor header text is not correct");
        ChainTestManager.takeScreenshot(driver, "Find Counsellor Header Verification");
    }
}
