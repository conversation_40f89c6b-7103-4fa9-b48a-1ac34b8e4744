package mto.ltc.eo.tests;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import mto.ltc.eo.base.BaseTest;

/**
 * Test class for Location Selection page
 */
public class LocationSelectionPageTest extends BaseTest {

    @BeforeClass
    public void locationSelectionSetup() {
        // Navigate to location selection page by filling client info form
        locationSelectionPage = clientInfoPage.fillClientInfoForm(
            "John", // firstName
            "M", // middleName
            "Doe", // lastName
            "Johnny", // preferredFirstName
            "", // preferredLastName
            "123 Main St", // address
            "Toronto", // city
            "M1M1P2", // postalCode
            "<EMAIL>", // email
            "4161234567", // phone
            "English" // preferredLanguage
        );
    }
    
    @Test(priority = 1, description = "Test location selection page header")
    public void testLocationSelectionPageHeaderTest() {
        Assert.assertTrue(locationSelectionPage.isPageHeaderDisplayed(), "Location selection page header is not displayed");
        String headerText = locationSelectionPage.getPageHeaderText();
        Assert.assertEquals(headerText, "Select a location", "Location selection page header is not correct");
    }
    
    @Test(priority = 2, description = "Test locations are available")
    public void testLocationsAvailableTest() {
        int numberOfLocations = locationSelectionPage.getNumberOfLocations();
        Assert.assertTrue(numberOfLocations > 0, "No locations are available");
        System.out.println("Number of available locations: " + numberOfLocations);
    }
    
    @Test(priority = 3, description = "Test location selection and navigation to confirmation page")
    public void testLocationSelectionAndNavigationTest() {
        confirmationPage = locationSelectionPage.selectLocationAndContinue(0);
        Assert.assertTrue(confirmationPage.isPageHeaderDisplayed(), "Confirmation page is not displayed after location selection");
        String headerText = confirmationPage.getPageHeaderText();
        Assert.assertEquals(headerText, "Confirm and Submit", "Confirmation page header is not correct");
    }
}
