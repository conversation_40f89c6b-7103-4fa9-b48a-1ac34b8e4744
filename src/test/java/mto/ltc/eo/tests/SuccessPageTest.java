package mto.ltc.eo.tests;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import mto.ltc.eo.base.BaseTest;

/**
 * Test class for Success page
 */
public class SuccessPageTest extends BaseTest {

    @BeforeClass
    public void successPageSetup() {
        // Navigate to success page by filling client info form, selecting location, and confirming
        locationSelectionPage = clientInfoPage.fillClientInfoForm(
            "John", // firstName
            "M", // middleName
            "Doe", // lastName
            "Johnny", // preferredFirstName
            "", // preferredLastName
            "123 Main St", // address
            "Toronto", // city
            "M1M1P2", // postalCode
            "<EMAIL>", // email
            "4161234567", // phone
            "English" // preferredLanguage
        );
        
        confirmationPage = locationSelectionPage.selectLocationAndContinue(0);
        successPage = confirmationPage.clickConfirmAndSubmitButton();
    }
    
    @Test(priority = 1, description = "Test success message is displayed")
    public void testSuccessMessageTest() {
        Assert.assertTrue(successPage.isSuccessMessageDisplayed(), "Success message is not displayed");
        String successMessage = successPage.getSuccessMessageText();
        Assert.assertEquals(successMessage, "APPLICATION FORM COMPLETED", "Success message is not correct");
    }
    
    @Test(priority = 2, description = "Test check circle icon is displayed")
    public void testCheckCircleIconTest() {
        Assert.assertTrue(successPage.isCheckCircleIconDisplayed(), "Check circle icon is not displayed");
    }
    
    @Test(priority = 3, description = "Test confirmation text is displayed")
    public void testConfirmationTextTest() {
        Assert.assertTrue(successPage.isConfirmationTextDisplayed(), "Confirmation text is not displayed");
        String confirmationText = successPage.getConfirmationText();
        Assert.assertTrue(confirmationText.contains("You have completed the form to find an employment counsellor"), 
                         "Confirmation text is not correct");
    }
    
    @Test(priority = 4, description = "Test application ID is displayed")
    public void testApplicationIdTest() {
        Assert.assertTrue(successPage.isApplicationIdTextDisplayed(), "Application ID text is not displayed");
        String applicationId = successPage.getApplicationId();
        Assert.assertFalse(applicationId.isEmpty(), "Application ID is empty");
        System.out.println("Application ID: " + applicationId);
    }
    
    @Test(priority = 5, description = "Test email confirmation is displayed")
    public void testEmailConfirmationTest() {
        Assert.assertTrue(successPage.isEmailConfirmationTextDisplayed(), "Email confirmation text is not displayed");
        String email = successPage.getEmailFromConfirmationText();
        Assert.assertFalse(email.isEmpty(), "Email is empty");
        System.out.println("Email: " + email);
    }
    
    @Test(priority = 6, description = "Test service language is displayed")
    public void testServiceLanguageTest() {
        Assert.assertTrue(successPage.isServiceLanguageTextDisplayed(), "Service language text is not displayed");
        String serviceLanguage = successPage.getServiceLanguage();
        Assert.assertEquals(serviceLanguage, "English", "Service language is not correct");
    }
    
    @Test(priority = 7, description = "Test location text is displayed")
    public void testLocationTextTest() {
        Assert.assertTrue(successPage.isLocationTextDisplayed(), "Location text is not displayed");
        String locationText = successPage.getLocationText();
        Assert.assertFalse(locationText.isEmpty(), "Location text is empty");
        System.out.println("Location: " + locationText);
    }
}
