package mto.ltc.eo.tests;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import mto.ltc.eo.base.BaseTest;

/**
 * Test class for Confirmation page
 */
public class ConfirmationPageTest extends BaseTest {

    @BeforeClass
    public void confirmationPageSetup() {
        // Navigate to confirmation page by filling client info form and selecting location
        locationSelectionPage = clientInfoPage.fillClientInfoForm(
            "John", // firstName
            "M", // middleName
            "Doe", // lastName
            "Johnny", // preferredFirstName
            "", // preferredLastName
            "123 Main St", // address
            "Toronto", // city
            "M1M1P2", // postalCode
            "<EMAIL>", // email
            "4161234567", // phone
            "English" // preferredLanguage
        );
        
        confirmationPage = locationSelectionPage.selectLocationAndContinue(0);
    }
    
    @Test(priority = 1, description = "Test confirmation page header")
    public void testConfirmationPageHeaderTest() {
        Assert.assertTrue(confirmationPage.isPageHeaderDisplayed(), "Confirmation page header is not displayed");
        String headerText = confirmationPage.getPageHeaderText();
        Assert.assertEquals(headerText, "Confirm and Submit", "Confirmation page header is not correct");
    }
    
    @Test(priority = 2, description = "Test location details are displayed")
    public void testLocationDetailsTest() {
        Assert.assertTrue(confirmationPage.isLocationDetailsDisplayed(), "Location details are not displayed");
        String locationDetails = confirmationPage.getLocationDetailsText();
        Assert.assertFalse(locationDetails.isEmpty(), "Location details are empty");
        System.out.println("Location details: " + locationDetails);
    }
    
    @Test(priority = 3, description = "Test back button navigation")
    public void testBackButtonNavigationTest() {
        locationSelectionPage = confirmationPage.clickBackButton();
        Assert.assertTrue(locationSelectionPage.isPageHeaderDisplayed(), "Location selection page is not displayed after clicking back button");
        
        // Navigate back to confirmation page for next test
        confirmationPage = locationSelectionPage.clickContinueButton();
    }
    
    @Test(priority = 4, description = "Test confirm and submit button navigation")
    public void testConfirmAndSubmitButtonNavigationTest() {
        successPage = confirmationPage.clickConfirmAndSubmitButton();
        Assert.assertTrue(successPage.isSuccessMessageDisplayed(), "Success page is not displayed after clicking confirm and submit button");
    }
}
