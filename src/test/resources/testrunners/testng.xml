<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Employment Ontario Automation Suite">
    <!-- ChainTest Listener -->
    <listeners>
        <listener class-name="com.aventstack.chaintest.testng.ChainTestNGListener" />
    </listeners>
    <!-- Global parameters for all tests -->
    <!-- Comment out these parameters to use values from config.properties -->
    <!-- <parameter name="browser" value="chrome" /> -->
    <!-- <parameter name="browserversion" value="latest" /> -->

    <test name="Header Tests">
        <classes>
            <class name="mto.ltc.eo.tests.HeaderTest" />
        </classes>
    </test>

    <test name="ESI Program Tests">
        <classes>
            <class name="mto.ltc.eo.tests.ESIProgramTest" />
        </classes>
    </test>

    <test name="Client Info Page Tests">
        <classes>
            <class name="mto.ltc.eo.tests.ClientInfoPageTest" />
        </classes>
    </test>

    <test name="Location Selection Page Tests">
        <classes>
            <class name="mto.ltc.eo.tests.LocationSelectionPageTest" />
        </classes>
    </test>

    <test name="Confirmation Page Tests">
        <classes>
            <class name="mto.ltc.eo.tests.ConfirmationPageTest" />
        </classes>
    </test>

    <test name="Success Page Tests">
        <classes>
            <class name="mto.ltc.eo.tests.SuccessPageTest" />
        </classes>
    </test>
</suite>
