<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Employment Ontario Automation Suite">
    <test name="Header Tests">
        <parameter name="browser" value="chrome" />
        <parameter name="browserversion" value="latest" />
        <classes>
            <class name="mto.ltc.eo.tests.HeaderTest" />
        </classes>
    </test>
    
    <test name="ESI Program Tests">
        <parameter name="browser" value="chrome" />
        <parameter name="browserversion" value="latest" />
        <classes>
            <class name="mto.ltc.eo.tests.ESIProgramTest" />
        </classes>
    </test>
    
    <test name="Client Info Page Tests">
        <parameter name="browser" value="chrome" />
        <parameter name="browserversion" value="latest" />
        <classes>
            <class name="mto.ltc.eo.tests.ClientInfoPageTest" />
        </classes>
    </test>
    
    <test name="Location Selection Page Tests">
        <parameter name="browser" value="chrome" />
        <parameter name="browserversion" value="latest" />
        <classes>
            <class name="mto.ltc.eo.tests.LocationSelectionPageTest" />
        </classes>
    </test>
    
    <test name="Confirmation Page Tests">
        <parameter name="browser" value="chrome" />
        <parameter name="browserversion" value="latest" />
        <classes>
            <class name="mto.ltc.eo.tests.ConfirmationPageTest" />
        </classes>
    </test>
    
    <test name="Success Page Tests">
        <parameter name="browser" value="chrome" />
        <parameter name="browserversion" value="latest" />
        <classes>
            <class name="mto.ltc.eo.tests.SuccessPageTest" />
        </classes>
    </test>
</suite>
