# ChainTest Configuration for Employment Ontario Automation Framework

# Project Information
chaintest.project.name=Employment Ontario Automation
chaintest.report.title=Employment Ontario Test Execution Report
chaintest.report.name=EO_Automation_Report

# Report Output Directory
chaintest.report.dir=test-output/chaintest-reports

# Screenshot Configuration
chaintest.screenshot.on.failure=true
chaintest.screenshot.on.pass=true
chaintest.screenshot.on.skip=false

# Report Generators
# Simple Generator (HTML Reports)
chaintest.generator.simple.enabled=true
chaintest.generator.simple.theme=dark
chaintest.generator.simple.encoding=UTF-8

# Email Generator (Disabled by default)
chaintest.generator.email.enabled=false
chaintest.generator.email.host=smtp.gmail.com
chaintest.generator.email.port=587
chaintest.generator.email.username=
chaintest.generator.email.password=
chaintest.generator.email.to=
chaintest.generator.email.from=
chaintest.generator.email.subject=Employment Ontario Test Results

# ChainLP Generator (Disabled by default)
chaintest.generator.chainlp.enabled=false

# Test Environment Information
chaintest.environment.name=QA
chaintest.environment.url=https://qa4.employmentontario.labour.gov.on.ca/rasp/
chaintest.environment.browser=Chrome
chaintest.environment.os=Windows

# Additional System Information
chaintest.system.info.tester=QA Team
chaintest.system.info.organization=Ministry of Labour, Immigration, Training and Skills Development
chaintest.system.info.build=EO-1.0
chaintest.system.info.version=1.0.0
