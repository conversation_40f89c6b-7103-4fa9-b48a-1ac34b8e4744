<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Console <PERSON>ppender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- File Appender for All Logs -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/employment-ontario-automation.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/employment-ontario-automation.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- File Appender for Test Results Only -->
    <appender name="TEST_RESULTS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/test-results.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/test-results.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>5MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>return message.contains("TEST RESULT") || message.contains("Test Started") || message.contains("Test Passed") || message.contains("Test Failed") || message.contains("Test Skipped");</expression>
            </evaluator>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
    </appender>
    
    <!-- File Appender for Browser Actions Only -->
    <appender name="BROWSER_ACTIONS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/browser-actions.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/browser-actions.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>5MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>return message.contains("BROWSER ACTION") || message.contains("ELEMENT INTERACTION");</expression>
            </evaluator>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
    </appender>
    
    <!-- Logger for specific packages -->
    <logger name="mto.ltc.eo" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="TEST_RESULTS" />
        <appender-ref ref="BROWSER_ACTIONS" />
    </logger>
    
    <!-- Logger for Selenium -->
    <logger name="org.openqa.selenium" level="WARN" />
    
    <!-- Logger for TestNG -->
    <logger name="org.testng" level="INFO" />
    
    <!-- Logger for ExtentReports -->
    <logger name="com.aventstack.extentreports" level="INFO" />
    
    <!-- Logger for ChainTest -->
    <logger name="com.aventstack.chaintest" level="INFO" />
    
    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>
