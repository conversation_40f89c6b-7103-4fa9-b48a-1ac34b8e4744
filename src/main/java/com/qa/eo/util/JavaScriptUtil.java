package com.qa.eo.util;

import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

/**
 * Utility class for JavaScript operations
 */
public class JavaScriptUtil {

    private WebDriver driver;
    private JavascriptExecutor js;

    public JavaScriptUtil(WebDriver driver) {
        this.driver = driver;
        this.js = (JavascriptExecutor) driver;
    }

    /**
     * Flash the element by changing its background color
     * 
     * @param element WebElement to flash
     */
    public void flash(WebElement element) {
        String bgcolor = element.getCssValue("backgroundColor");
        for (int i = 0; i < 3; i++) {
            changeColor("rgb(0,200,0)", element);
            changeColor(bgcolor, element);
        }
    }

    /**
     * Change the background color of an element
     * 
     * @param color   Color to change to
     * @param element WebElement to change color of
     */
    private void changeColor(String color, WebElement element) {
        js.executeScript("arguments[0].style.backgroundColor = '" + color + "'", element);
        try {
            Thread.sleep(20);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * Draw a border around an element
     * 
     * @param element WebElement to draw border around
     */
    public void drawBorder(WebElement element) {
        js.executeScript("arguments[0].style.border='3px solid red'", element);
    }

    /**
     * Generate an alert with a message
     * 
     * @param message Message to display in alert
     */
    public void generateAlert(String message) {
        js.executeScript("alert('" + message + "')");
    }

    /**
     * Click on an element using JavaScript
     * 
     * @param element WebElement to click
     */
    public void clickElementByJS(WebElement element) {
        js.executeScript("arguments[0].click();", element);
    }

    /**
     * Refresh the page using JavaScript
     */
    public void refreshBrowserByJS() {
        js.executeScript("history.go(0)");
    }

    /**
     * Get the page title using JavaScript
     * 
     * @return Page title
     */
    public String getTitleByJS() {
        return js.executeScript("return document.title;").toString();
    }

    /**
     * Get the inner text of the page using JavaScript
     * 
     * @return Inner text of the page
     */
    public String getPageInnerText() {
        return js.executeScript("return document.documentElement.innerText;").toString();
    }

    /**
     * Scroll to the bottom of the page
     */
    public void scrollPageDown() {
        js.executeScript("window.scrollTo(0, document.body.scrollHeight)");
    }

    /**
     * Scroll to a specific height
     * 
     * @param height Height to scroll to
     */
    public void scrollPageDown(String height) {
        js.executeScript("window.scrollTo(0, " + height + ")");
    }

    /**
     * Scroll to the top of the page
     */
    public void scrollPageUp() {
        js.executeScript("window.scrollTo(document.body.scrollHeight, 0)");
    }

    /**
     * Scroll into view of an element
     * 
     * @param element WebElement to scroll to
     */
    public void scrollIntoView(WebElement element) {
        js.executeScript("arguments[0].scrollIntoView(true);", element);
    }

    /**
     * Check if the page is fully loaded
     * 
     * @return true if page is fully loaded, false otherwise
     */
    public boolean isPageLoaded() {
        return js.executeScript("return document.readyState").toString().equals("complete");
    }

    /**
     * Send keys to an element using JavaScript
     * 
     * @param element WebElement to send keys to
     * @param value   Value to send
     */
    public void sendKeysUsingJSWithId(String id, String value) {
        js.executeScript("document.getElementById('" + id + "').value='" + value + "'");
    }

    /**
     * Send keys to an element using JavaScript
     * 
     * @param element WebElement to send keys to
     * @param value   Value to send
     */
    public void sendKeysUsingJSWithElement(WebElement element, String value) {
        js.executeScript("arguments[0].value='" + value + "'", element);
    }
}
