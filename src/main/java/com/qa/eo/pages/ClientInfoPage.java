package com.qa.eo.pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import com.qa.eo.constants.AppConstants;

/**
 * Page class for Client Information page (Step 1 of 3)
 */
public class ClientInfoPage extends BasePage {

    // Page locators
    private By esiProgramText = By.xpath("//p[contains(text(),'You are about to book an appointment with one of the 6 indigenous service providers')]");
    private By clickHereLink = By.xpath("//a[contains(text(),'click here')]");
    
    // Form fields
    private By firstNameInput = By.id("firstName");
    private By middleNameInput = By.id("middleName");
    private By lastNameInput = By.id("lastName");
    private By preferredFirstNameInput = By.id("preferredFirstName");
    private By preferredLastNameInput = By.id("preferredLastName");
    
    private By addressInput = By.id("address");
    private By cityInput = By.id("city");
    private By postalCodeInput = By.id("postalCode");
    
    private By emailInput = By.id("email");
    private By phoneInput = By.id("phone");
    
    private By preferredLanguageDropdown = By.id("preferredLanguage");
    
    private By continueButton = By.xpath("//button[contains(text(),'Continue')]");
    
    public ClientInfoPage(WebDriver driver) {
        super(driver);
    }
    
    /**
     * Check if ESI program text is displayed
     * 
     * @return true if ESI program text is displayed, false otherwise
     */
    public boolean isESIProgramTextDisplayed() {
        return elementUtil.doIsDisplayed(esiProgramText);
    }
    
    /**
     * Get ESI program text
     * 
     * @return ESI program text
     */
    public String getESIProgramText() {
        return elementUtil.doGetText(esiProgramText);
    }
    
    /**
     * Check if "click here" link is displayed
     * 
     * @return true if "click here" link is displayed, false otherwise
     */
    public boolean isClickHereLinkDisplayed() {
        return elementUtil.doIsDisplayed(clickHereLink);
    }
    
    /**
     * Click on "click here" link
     */
    public void clickOnClickHereLink() {
        elementUtil.doClick(clickHereLink);
    }
    
    /**
     * Fill client information form
     * 
     * @param firstName First name
     * @param middleName Middle name
     * @param lastName Last name
     * @param preferredFirstName Preferred first name
     * @param preferredLastName Preferred last name
     * @param address Address
     * @param city City
     * @param postalCode Postal code
     * @param email Email
     * @param phone Phone
     * @param preferredLanguage Preferred language
     * @return LocationSelectionPage
     */
    public LocationSelectionPage fillClientInfoForm(String firstName, String middleName, String lastName,
                                                  String preferredFirstName, String preferredLastName,
                                                  String address, String city, String postalCode,
                                                  String email, String phone, String preferredLanguage) {
        
        elementUtil.doSendKeys(firstNameInput, firstName);
        
        if (middleName != null && !middleName.isEmpty()) {
            elementUtil.doSendKeys(middleNameInput, middleName);
        }
        
        elementUtil.doSendKeys(lastNameInput, lastName);
        
        if (preferredFirstName != null && !preferredFirstName.isEmpty()) {
            elementUtil.doSendKeys(preferredFirstNameInput, preferredFirstName);
        }
        
        if (preferredLastName != null && !preferredLastName.isEmpty()) {
            elementUtil.doSendKeys(preferredLastNameInput, preferredLastName);
        }
        
        elementUtil.doSendKeys(addressInput, address);
        elementUtil.doSendKeys(cityInput, city);
        elementUtil.doSendKeys(postalCodeInput, postalCode);
        
        elementUtil.doSendKeys(emailInput, email);
        elementUtil.doSendKeys(phoneInput, phone);
        
        elementUtil.doSelectDropdownByVisibleText(preferredLanguageDropdown, preferredLanguage);
        
        elementUtil.doClick(continueButton);
        
        return new LocationSelectionPage(driver);
    }
    
    /**
     * Click continue button
     * 
     * @return LocationSelectionPage
     */
    public LocationSelectionPage clickContinueButton() {
        elementUtil.doClick(continueButton);
        return new LocationSelectionPage(driver);
    }
}
