package com.qa.eo.pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

/**
 * Page class for Success page (Final confirmation page)
 */
public class SuccessPage extends BasePage {

    // Page locators
    private By successMessage = By.xpath("//h2[contains(text(),'APPLICATION FORM COMPLETED')]");
    private By checkCircleIcon = By.xpath("//span[contains(text(),'check_circle')]");
    private By confirmationText = By.xpath("//p[contains(text(),'You have completed the form to find an employment counsellor')]");
    private By applicationIdText = By.xpath("//p[contains(text(),'Application ID:')]");
    private By emailConfirmationText = By.xpath("//p[contains(text(),'You will receive an email confirmation')]");
    private By serviceLanguageText = By.xpath("//p[contains(text(),'Service language:')]");
    private By locationText = By.xpath("//p[contains(text(),'Location:')]");
    
    public SuccessPage(WebDriver driver) {
        super(driver);
    }
    
    /**
     * Check if success message is displayed
     * 
     * @return true if success message is displayed, false otherwise
     */
    public boolean isSuccessMessageDisplayed() {
        return elementUtil.doIsDisplayed(successMessage);
    }
    
    /**
     * Get success message text
     * 
     * @return Success message text
     */
    public String getSuccessMessageText() {
        return elementUtil.doGetText(successMessage);
    }
    
    /**
     * Check if check circle icon is displayed
     * 
     * @return true if check circle icon is displayed, false otherwise
     */
    public boolean isCheckCircleIconDisplayed() {
        return elementUtil.doIsDisplayed(checkCircleIcon);
    }
    
    /**
     * Check if confirmation text is displayed
     * 
     * @return true if confirmation text is displayed, false otherwise
     */
    public boolean isConfirmationTextDisplayed() {
        return elementUtil.doIsDisplayed(confirmationText);
    }
    
    /**
     * Get confirmation text
     * 
     * @return Confirmation text
     */
    public String getConfirmationText() {
        return elementUtil.doGetText(confirmationText);
    }
    
    /**
     * Check if application ID text is displayed
     * 
     * @return true if application ID text is displayed, false otherwise
     */
    public boolean isApplicationIdTextDisplayed() {
        return elementUtil.doIsDisplayed(applicationIdText);
    }
    
    /**
     * Get application ID
     * 
     * @return Application ID
     */
    public String getApplicationId() {
        String applicationIdFullText = elementUtil.doGetText(applicationIdText);
        return applicationIdFullText.replace("Application ID:", "").trim();
    }
    
    /**
     * Check if email confirmation text is displayed
     * 
     * @return true if email confirmation text is displayed, false otherwise
     */
    public boolean isEmailConfirmationTextDisplayed() {
        return elementUtil.doIsDisplayed(emailConfirmationText);
    }
    
    /**
     * Get email from confirmation text
     * 
     * @return Email from confirmation text
     */
    public String getEmailFromConfirmationText() {
        String emailFullText = elementUtil.doGetText(emailConfirmationText);
        return emailFullText.replace("You will receive an email confirmation to:", "").trim();
    }
    
    /**
     * Check if service language text is displayed
     * 
     * @return true if service language text is displayed, false otherwise
     */
    public boolean isServiceLanguageTextDisplayed() {
        return elementUtil.doIsDisplayed(serviceLanguageText);
    }
    
    /**
     * Get service language
     * 
     * @return Service language
     */
    public String getServiceLanguage() {
        String serviceLanguageFullText = elementUtil.doGetText(serviceLanguageText);
        return serviceLanguageFullText.replace("Service language:", "").trim();
    }
    
    /**
     * Check if location text is displayed
     * 
     * @return true if location text is displayed, false otherwise
     */
    public boolean isLocationTextDisplayed() {
        return elementUtil.doIsDisplayed(locationText);
    }
    
    /**
     * Get location text
     * 
     * @return Location text
     */
    public String getLocationText() {
        return elementUtil.doGetText(locationText);
    }
}
