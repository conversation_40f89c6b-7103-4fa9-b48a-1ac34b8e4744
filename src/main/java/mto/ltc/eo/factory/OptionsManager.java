package com.qa.eo.factory;

import java.util.Properties;

import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.edge.EdgeOptions;
import org.openqa.selenium.firefox.FirefoxOptions;

/**
 * Class to manage browser options
 */
public class OptionsManager {

    private Properties prop;
    private ChromeOptions co;
    private FirefoxOptions fo;
    private EdgeOptions eo;

    public OptionsManager(Properties prop) {
        this.prop = prop;
    }

    /**
     * Get Chrome options
     * 
     * @return ChromeOptions
     */
    public ChromeOptions getChromeOptions() {
        co = new ChromeOptions();
        
        if (Boolean.parseBoolean(prop.getProperty("headless").trim())) {
            co.addArguments("--headless");
        }
        
        if (Boolean.parseBoolean(prop.getProperty("incognito").trim())) {
            co.addArguments("--incognito");
        }
        
        return co;
    }

    /**
     * Get Firefox options
     * 
     * @return FirefoxOptions
     */
    public FirefoxOptions getFirefoxOptions() {
        fo = new FirefoxOptions();
        
        if (Boolean.parseBoolean(prop.getProperty("headless").trim())) {
            fo.addArguments("--headless");
        }
        
        if (Boolean.parseBoolean(prop.getProperty("incognito").trim())) {
            fo.addArguments("--private");
        }
        
        return fo;
    }

    /**
     * Get Edge options
     * 
     * @return EdgeOptions
     */
    public EdgeOptions getEdgeOptions() {
        eo = new EdgeOptions();
        
        if (Boolean.parseBoolean(prop.getProperty("headless").trim())) {
            eo.addArguments("--headless");
        }
        
        if (Boolean.parseBoolean(prop.getProperty("incognito").trim())) {
            eo.addArguments("--inprivate");
        }
        
        return eo;
    }
}
