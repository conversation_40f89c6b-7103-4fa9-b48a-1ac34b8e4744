package mto.ltc.eo.factory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Properties;

import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.edge.EdgeDriver;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.io.FileHandler;
import org.openqa.selenium.safari.SafariDriver;

import mto.ltc.eo.constants.AppConstants;
import mto.ltc.eo.exception.FrameworkException;

import io.github.bonigarcia.wdm.WebDriverManager;

/**
 * Factory class for WebDriver initialization
 */
public class DriverFactory {

    private WebDriver driver;
    private Properties prop;
    private OptionsManager optionsManager;
    public static String highlight;
    
    private static final ThreadLocal<WebDriver> tlDriver = new ThreadLocal<>();

    /**
     * Initialize WebDriver based on browser name
     * 
     * @param prop Properties containing browser configuration
     * @return WebDriver instance
     */
    public WebDriver initDriver(Properties prop) {
        String browserName = prop.getProperty("browser").trim().toLowerCase();
        System.out.println("Browser name is: " + browserName);
        
        highlight = prop.getProperty("highlight").trim();
        optionsManager = new OptionsManager(prop);
        
        switch (browserName) {
            case "chrome":
                WebDriverManager.chromedriver().setup();
                tlDriver.set(new ChromeDriver(optionsManager.getChromeOptions()));
                break;
                
            case "firefox":
                WebDriverManager.firefoxdriver().setup();
                tlDriver.set(new FirefoxDriver(optionsManager.getFirefoxOptions()));
                break;
                
            case "edge":
                WebDriverManager.edgedriver().setup();
                tlDriver.set(new EdgeDriver(optionsManager.getEdgeOptions()));
                break;
                
            case "safari":
                tlDriver.set(new SafariDriver());
                break;
                
            default:
                System.out.println("Please pass the correct browser name: " + browserName);
                throw new FrameworkException("INVALID BROWSER");
        }
        
        getDriver().manage().deleteAllCookies();
        getDriver().manage().window().maximize();
        getDriver().get(prop.getProperty("url"));
        
        return getDriver();
    }

    /**
     * Get the WebDriver instance from ThreadLocal
     * 
     * @return WebDriver instance
     */
    public static WebDriver getDriver() {
        return tlDriver.get();
    }

    /**
     * Initialize Properties from config file
     * 
     * @return Properties instance
     */
    public Properties initProp() {
        prop = new Properties();
        FileInputStream ip = null;
        
        try {
            ip = new FileInputStream(AppConstants.CONFIG_FILE_PATH);
            prop.load(ip);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            throw new FrameworkException("Config file not found");
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return prop;
    }

    /**
     * Take screenshot
     * 
     * @return Path to screenshot
     */
    public static String getScreenshot() {
        File srcFile = ((TakesScreenshot) getDriver()).getScreenshotAs(OutputType.FILE);
        String path = System.getProperty("user.dir") + "/screenshots/" + System.currentTimeMillis() + ".png";
        File destination = new File(path);
        
        try {
            FileHandler.copy(srcFile, destination);
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return path;
    }
}
