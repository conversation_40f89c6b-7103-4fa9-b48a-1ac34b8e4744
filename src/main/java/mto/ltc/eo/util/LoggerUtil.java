package mto.ltc.eo.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for logging
 */
public class LoggerUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(LoggerUtil.class);
    
    /**
     * Log info message
     * 
     * @param message Message to log
     */
    public static void info(String message) {
        logger.info(message);
    }
    
    /**
     * Log debug message
     * 
     * @param message Message to log
     */
    public static void debug(String message) {
        logger.debug(message);
    }
    
    /**
     * Log warning message
     * 
     * @param message Message to log
     */
    public static void warn(String message) {
        logger.warn(message);
    }
    
    /**
     * Log error message
     * 
     * @param message Message to log
     */
    public static void error(String message) {
        logger.error(message);
    }
    
    /**
     * Log error message with exception
     * 
     * @param message Message to log
     * @param throwable Exception to log
     */
    public static void error(String message, Throwable throwable) {
        logger.error(message, throwable);
    }
    
    /**
     * Log test step
     * 
     * @param stepDescription Description of the test step
     */
    public static void logStep(String stepDescription) {
        logger.info("STEP: {}", stepDescription);
    }
    
    /**
     * Log test result
     * 
     * @param testName Name of the test
     * @param result Result of the test (PASS/FAIL/SKIP)
     */
    public static void logTestResult(String testName, String result) {
        logger.info("TEST RESULT: {} - {}", testName, result);
    }
    
    /**
     * Log browser action
     * 
     * @param action Browser action performed
     */
    public static void logBrowserAction(String action) {
        logger.info("BROWSER ACTION: {}", action);
    }
    
    /**
     * Log element interaction
     * 
     * @param element Element being interacted with
     * @param action Action performed on the element
     */
    public static void logElementInteraction(String element, String action) {
        logger.info("ELEMENT INTERACTION: {} - {}", action, element);
    }
}
