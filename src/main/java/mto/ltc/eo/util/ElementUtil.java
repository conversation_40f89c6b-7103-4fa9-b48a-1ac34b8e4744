package com.qa.eo.util;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

import org.openqa.selenium.Alert;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoAlertPresentException;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.StaleElementReferenceException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.Wait;
import org.openqa.selenium.support.ui.WebDriverWait;

/**
 * Utility class for element operations
 */
public class ElementUtil {

    private WebDriver driver;
    private JavaScriptUtil jsUtil;

    public ElementUtil(WebDriver driver) {
        this.driver = driver;
        this.jsUtil = new JavaScriptUtil(driver);
    }

    /**
     * Get a WebElement using a By locator
     * 
     * @param locator By locator to find element
     * @return WebElement found
     */
    public WebElement getElement(By locator) {
        WebElement element = driver.findElement(locator);
        return element;
    }

    /**
     * Get a list of WebElements using a By locator
     * 
     * @param locator By locator to find elements
     * @return List of WebElements found
     */
    public List<WebElement> getElements(By locator) {
        return driver.findElements(locator);
    }

    /**
     * Click on an element
     * 
     * @param locator By locator of element to click
     */
    public void doClick(By locator) {
        getElement(locator).click();
    }

    /**
     * Click on an element
     * 
     * @param element WebElement to click
     */
    public void doClick(WebElement element) {
        element.click();
    }

    /**
     * Send keys to an element
     * 
     * @param locator By locator of element to send keys to
     * @param value   Value to send
     */
    public void doSendKeys(By locator, String value) {
        WebElement element = getElement(locator);
        element.clear();
        element.sendKeys(value);
    }

    /**
     * Send keys to an element
     * 
     * @param element WebElement to send keys to
     * @param value   Value to send
     */
    public void doSendKeys(WebElement element, String value) {
        element.clear();
        element.sendKeys(value);
    }

    /**
     * Get text from an element
     * 
     * @param locator By locator of element to get text from
     * @return Text of element
     */
    public String doGetText(By locator) {
        return getElement(locator).getText();
    }

    /**
     * Get text from an element
     * 
     * @param element WebElement to get text from
     * @return Text of element
     */
    public String doGetText(WebElement element) {
        return element.getText();
    }

    /**
     * Check if an element is displayed
     * 
     * @param locator By locator of element to check
     * @return true if element is displayed, false otherwise
     */
    public boolean doIsDisplayed(By locator) {
        try {
            return getElement(locator).isDisplayed();
        } catch (NoSuchElementException e) {
            return false;
        }
    }

    /**
     * Check if an element is displayed
     * 
     * @param element WebElement to check
     * @return true if element is displayed, false otherwise
     */
    public boolean doIsDisplayed(WebElement element) {
        try {
            return element.isDisplayed();
        } catch (NoSuchElementException e) {
            return false;
        }
    }

    /**
     * Check if an element is enabled
     * 
     * @param locator By locator of element to check
     * @return true if element is enabled, false otherwise
     */
    public boolean doIsEnabled(By locator) {
        return getElement(locator).isEnabled();
    }

    /**
     * Check if an element is selected
     * 
     * @param locator By locator of element to check
     * @return true if element is selected, false otherwise
     */
    public boolean doIsSelected(By locator) {
        return getElement(locator).isSelected();
    }

    /**
     * Get attribute value from an element
     * 
     * @param locator     By locator of element to get attribute from
     * @param attrName    Name of attribute to get
     * @return Attribute value
     */
    public String doGetAttribute(By locator, String attrName) {
        return getElement(locator).getAttribute(attrName);
    }

    /**
     * Get CSS value from an element
     * 
     * @param locator     By locator of element to get CSS value from
     * @param cssProperty CSS property to get
     * @return CSS value
     */
    public String doGetCSSValue(By locator, String cssProperty) {
        return getElement(locator).getCssValue(cssProperty);
    }

    /**
     * Select option from dropdown by visible text
     * 
     * @param locator By locator of dropdown
     * @param text    Visible text to select
     */
    public void doSelectDropdownByVisibleText(By locator, String text) {
        Select select = new Select(getElement(locator));
        select.selectByVisibleText(text);
    }

    /**
     * Select option from dropdown by value
     * 
     * @param locator By locator of dropdown
     * @param value   Value to select
     */
    public void doSelectDropdownByValue(By locator, String value) {
        Select select = new Select(getElement(locator));
        select.selectByValue(value);
    }

    /**
     * Select option from dropdown by index
     * 
     * @param locator By locator of dropdown
     * @param index   Index to select
     */
    public void doSelectDropdownByIndex(By locator, int index) {
        Select select = new Select(getElement(locator));
        select.selectByIndex(index);
    }

    /**
     * Get all options from dropdown
     * 
     * @param locator By locator of dropdown
     * @return List of options as WebElements
     */
    public List<WebElement> doGetDropdownOptions(By locator) {
        Select select = new Select(getElement(locator));
        return select.getOptions();
    }

    /**
     * Get all options text from dropdown
     * 
     * @param locator By locator of dropdown
     * @return List of options text
     */
    public List<String> doGetDropdownOptionsText(By locator) {
        List<WebElement> optionsList = doGetDropdownOptions(locator);
        List<String> optionsTextList = new ArrayList<String>();
        for (WebElement e : optionsList) {
            optionsTextList.add(e.getText());
        }
        return optionsTextList;
    }

    /**
     * Select value from dropdown without using Select class
     * 
     * @param locator By locator of dropdown
     * @param value   Value to select
     */
    public void doSelectValueFromDropdownWithoutSelect(By locator, String value) {
        List<WebElement> optionsList = getElements(locator);
        for (WebElement e : optionsList) {
            if (e.getText().equals(value)) {
                e.click();
                break;
            }
        }
    }

    /**
     * Move to an element using Actions class
     * 
     * @param locator By locator of element to move to
     */
    public void doMoveToElement(By locator) {
        Actions act = new Actions(driver);
        act.moveToElement(getElement(locator)).perform();
    }

    /**
     * Click on an element using Actions class
     * 
     * @param locator By locator of element to click
     */
    public void doActionsClick(By locator) {
        Actions act = new Actions(driver);
        act.click(getElement(locator)).perform();
    }

    /**
     * Send keys to an element using Actions class
     * 
     * @param locator By locator of element to send keys to
     * @param value   Value to send
     */
    public void doActionsSendKeys(By locator, String value) {
        Actions act = new Actions(driver);
        act.sendKeys(getElement(locator), value).perform();
    }

    /**
     * Wait for element to be present
     * 
     * @param locator  By locator of element to wait for
     * @param timeOut  Timeout in seconds
     * @return WebElement found
     */
    public WebElement waitForElementPresence(By locator, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.presenceOfElementLocated(locator));
    }

    /**
     * Wait for element to be visible
     * 
     * @param locator  By locator of element to wait for
     * @param timeOut  Timeout in seconds
     * @return WebElement found
     */
    public WebElement waitForElementVisible(By locator, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.visibilityOfElementLocated(locator));
    }

    /**
     * Wait for element to be clickable
     * 
     * @param locator  By locator of element to wait for
     * @param timeOut  Timeout in seconds
     * @return WebElement found
     */
    public WebElement waitForElementToBeClickable(By locator, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.elementToBeClickable(locator));
    }

    /**
     * Wait for element to be clickable and click it
     * 
     * @param locator  By locator of element to wait for and click
     * @param timeOut  Timeout in seconds
     */
    public void clickWhenReady(By locator, int timeOut) {
        waitForElementToBeClickable(locator, timeOut).click();
    }

    /**
     * Wait for alert to be present
     * 
     * @param timeOut  Timeout in seconds
     * @return Alert found
     */
    public Alert waitForAlert(int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.alertIsPresent());
    }

    /**
     * Wait for title to contain specified text
     * 
     * @param titleFraction  Title fraction to wait for
     * @param timeOut        Timeout in seconds
     * @return true if title contains specified text, false otherwise
     */
    public boolean waitForTitleContains(String titleFraction, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.titleContains(titleFraction));
    }

    /**
     * Wait for title to be exactly as specified
     * 
     * @param title    Title to wait for
     * @param timeOut  Timeout in seconds
     * @return true if title is exactly as specified, false otherwise
     */
    public boolean waitForTitleIs(String title, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.titleIs(title));
    }

    /**
     * Wait for URL to contain specified text
     * 
     * @param urlFraction  URL fraction to wait for
     * @param timeOut      Timeout in seconds
     * @return true if URL contains specified text, false otherwise
     */
    public boolean waitForURLContains(String urlFraction, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.urlContains(urlFraction));
    }

    /**
     * Wait for URL to be exactly as specified
     * 
     * @param url      URL to wait for
     * @param timeOut  Timeout in seconds
     * @return true if URL is exactly as specified, false otherwise
     */
    public boolean waitForURLToBe(String url, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.urlToBe(url));
    }

    /**
     * Wait for frame to be available and switch to it
     * 
     * @param frameLocator  By locator of frame
     * @param timeOut       Timeout in seconds
     */
    public void waitForFrameAndSwitchToIt(By frameLocator, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        wait.until(ExpectedConditions.frameToBeAvailableAndSwitchToIt(frameLocator));
    }

    /**
     * Wait for frame to be available and switch to it
     * 
     * @param frameIndex  Index of frame
     * @param timeOut     Timeout in seconds
     */
    public void waitForFrameAndSwitchToIt(int frameIndex, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        wait.until(ExpectedConditions.frameToBeAvailableAndSwitchToIt(frameIndex));
    }

    /**
     * Wait for frame to be available and switch to it
     * 
     * @param frameName  Name or ID of frame
     * @param timeOut    Timeout in seconds
     */
    public void waitForFrameAndSwitchToIt(String frameName, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        wait.until(ExpectedConditions.frameToBeAvailableAndSwitchToIt(frameName));
    }

    /**
     * Wait for elements to be visible
     * 
     * @param locator  By locator of elements to wait for
     * @param timeOut  Timeout in seconds
     * @return List of WebElements found
     */
    public List<WebElement> waitForElementsVisible(By locator, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.visibilityOfAllElementsLocatedBy(locator));
    }

    /**
     * Wait for elements to be present
     * 
     * @param locator  By locator of elements to wait for
     * @param timeOut  Timeout in seconds
     * @return List of WebElements found
     */
    public List<WebElement> waitForElementsPresence(By locator, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.presenceOfAllElementsLocatedBy(locator));
    }

    /**
     * Wait for element using FluentWait
     * 
     * @param locator      By locator of element to wait for
     * @param timeOut      Timeout in seconds
     * @param pollingTime  Polling time in milliseconds
     * @return WebElement found
     */
    public WebElement waitForElementVisibleWithFluentWait(By locator, int timeOut, long pollingTime) {
        Wait<WebDriver> wait = new FluentWait<WebDriver>(driver)
                .withTimeout(Duration.ofSeconds(timeOut))
                .pollingEvery(Duration.ofMillis(pollingTime))
                .ignoring(NoSuchElementException.class)
                .ignoring(StaleElementReferenceException.class);

        return wait.until(ExpectedConditions.visibilityOfElementLocated(locator));
    }

    /**
     * Wait for alert using FluentWait
     * 
     * @param timeOut      Timeout in seconds
     * @param pollingTime  Polling time in milliseconds
     * @return Alert found
     */
    public Alert waitForAlertWithFluentWait(int timeOut, long pollingTime) {
        Wait<WebDriver> wait = new FluentWait<WebDriver>(driver)
                .withTimeout(Duration.ofSeconds(timeOut))
                .pollingEvery(Duration.ofMillis(pollingTime))
                .ignoring(NoAlertPresentException.class);

        return wait.until(ExpectedConditions.alertIsPresent());
    }

    /**
     * Check if a new window is opened
     * 
     * @param expectedNumberOfWindows  Expected number of windows
     * @param timeOut                  Timeout in seconds
     * @return true if expected number of windows is present, false otherwise
     */
    public boolean waitForNumberOfWindows(int expectedNumberOfWindows, int timeOut) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeOut));
        return wait.until(ExpectedConditions.numberOfWindowsToBe(expectedNumberOfWindows));
    }

    /**
     * Switch to a specific window by index
     * 
     * @param windowIndex  Index of window to switch to
     */
    public void switchToWindow(int windowIndex) {
        List<String> windowHandles = new ArrayList<>(driver.getWindowHandles());
        driver.switchTo().window(windowHandles.get(windowIndex));
    }

    /**
     * Get the current page title
     * 
     * @return Current page title
     */
    public String getPageTitle() {
        return driver.getTitle();
    }

    /**
     * Get the current page URL
     * 
     * @return Current page URL
     */
    public String getPageURL() {
        return driver.getCurrentUrl();
    }
}
