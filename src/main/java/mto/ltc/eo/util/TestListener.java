package mto.ltc.eo.util;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.MediaEntityBuilder;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.ExtentColor;
import com.aventstack.extentreports.markuputils.MarkupHelper;
import mto.ltc.eo.factory.DriverFactory;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.testng.ITestContext;
import org.testng.ITestListener;
import org.testng.ITestResult;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * Enhanced TestNG listener for ExtentReports with comprehensive logging
 */
public class TestListener implements ITestListener {

    private static ExtentReports extent = ExtentReportManager.getInstance();
    private static ThreadLocal<ExtentTest> test = new ThreadLocal<>();
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public void onStart(ITestContext context) {
        LoggerUtil.info("=== Test Suite Started: " + context.getName() + " ===");
        LoggerUtil.info("Start Time: " + dateFormat.format(new Date()));
        LoggerUtil.info("Total Tests to Run: " + context.getAllTestMethods().length);
    }

    @Override
    public void onTestStart(ITestResult result) {
        String methodName = result.getMethod().getMethodName();
        String className = result.getTestClass().getName();
        String testName = className + " - " + methodName;
        String description = result.getMethod().getDescription();

        LoggerUtil.info("=== Test Started: " + methodName + " ===");
        LoggerUtil.info("Test Description: " + (description != null ? description : "No description"));
        LoggerUtil.info("Test Class: " + className);
        LoggerUtil.info("Start Time: " + dateFormat.format(new Date(result.getStartMillis())));

        ExtentTest extentTest = extent.createTest(testName, description);
        test.set(extentTest);
        ReportManager.setTest(extentTest);

        extentTest.info("Test started: " + methodName);
        extentTest.info("Test Class: " + className);
        extentTest.info("Start Time: " + dateFormat.format(new Date(result.getStartMillis())));
    }

    @Override
    public void onTestSuccess(ITestResult result) {
        String methodName = result.getMethod().getMethodName();
        long duration = result.getEndMillis() - result.getStartMillis();

        LoggerUtil.info("=== Test Passed: " + methodName + " ===");
        LoggerUtil.info("Duration: " + duration + " ms");
        LoggerUtil.info("End Time: " + dateFormat.format(new Date(result.getEndMillis())));

        test.get().log(Status.PASS, MarkupHelper.createLabel("Test Passed", ExtentColor.GREEN));
        test.get().info("Test Duration: " + duration + " ms");
        test.get().info("End Time: " + dateFormat.format(new Date(result.getEndMillis())));

        // Capture screenshot on success (optional)
        WebDriver driver = DriverFactory.getDriver();
        if (driver != null) {
            try {
                String base64Screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.BASE64);
                test.get().pass("Test passed screenshot",
                        MediaEntityBuilder.createScreenCaptureFromBase64String(base64Screenshot).build());
                LoggerUtil.info("Success screenshot captured for: " + methodName);
            } catch (Exception e) {
                LoggerUtil.warn("Failed to capture success screenshot for: " + methodName);
            }
        }
    }

    @Override
    public void onTestFailure(ITestResult result) {
        String methodName = result.getMethod().getMethodName();
        long duration = result.getEndMillis() - result.getStartMillis();
        Throwable throwable = result.getThrowable();

        LoggerUtil.error("=== Test Failed: " + methodName + " ===");
        LoggerUtil.error("Duration: " + duration + " ms");
        LoggerUtil.error("End Time: " + dateFormat.format(new Date(result.getEndMillis())));
        LoggerUtil.error("Failure Reason: " + (throwable != null ? throwable.getMessage() : "Unknown"));

        test.get().log(Status.FAIL, MarkupHelper.createLabel("Test Failed", ExtentColor.RED));
        test.get().fail(throwable);
        test.get().info("Test Duration: " + duration + " ms");
        test.get().info("End Time: " + dateFormat.format(new Date(result.getEndMillis())));

        // Capture screenshot on failure
        WebDriver driver = DriverFactory.getDriver();
        if (driver != null) {
            try {
                String base64Screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.BASE64);
                test.get().fail("Test failed screenshot",
                        MediaEntityBuilder.createScreenCaptureFromBase64String(base64Screenshot).build());
                LoggerUtil.info("Failure screenshot captured for: " + methodName);
            } catch (Exception e) {
                LoggerUtil.warn("Failed to capture failure screenshot for: " + methodName);
            }
        }
    }

    @Override
    public void onTestSkipped(ITestResult result) {
        String methodName = result.getMethod().getMethodName();
        Throwable throwable = result.getThrowable();

        LoggerUtil.warn("=== Test Skipped: " + methodName + " ===");
        LoggerUtil.warn("Skip Reason: " + (throwable != null ? throwable.getMessage() : "Unknown"));
        LoggerUtil.warn("End Time: " + dateFormat.format(new Date(result.getEndMillis())));

        test.get().log(Status.SKIP, MarkupHelper.createLabel("Test Skipped", ExtentColor.YELLOW));
        test.get().skip(throwable);
        test.get().info("End Time: " + dateFormat.format(new Date(result.getEndMillis())));
    }

    @Override
    public void onTestFailedButWithinSuccessPercentage(ITestResult result) {
        String methodName = result.getMethod().getMethodName();
        LoggerUtil.warn("Test failed but within success percentage: " + methodName);
        onTestFailure(result);
    }

    @Override
    public void onFinish(ITestContext context) {
        LoggerUtil.info("=== Test Suite Finished: " + context.getName() + " ===");
        LoggerUtil.info("End Time: " + dateFormat.format(new Date()));
        LoggerUtil.info("Total Tests Run: " + (context.getPassedTests().size() + context.getFailedTests().size() + context.getSkippedTests().size()));
        LoggerUtil.info("Passed Tests: " + context.getPassedTests().size());
        LoggerUtil.info("Failed Tests: " + context.getFailedTests().size());
        LoggerUtil.info("Skipped Tests: " + context.getSkippedTests().size());

        if (extent != null) {
            extent.flush();
            LoggerUtil.info("ExtentReports flushed successfully");
        }

        // Clean up ThreadLocal
        test.remove();
        ReportManager.removeTest();
    }

    /**
     * Log a step in the current test
     *
     * @param stepDescription Description of the step
     */
    public static void logStep(String stepDescription) {
        Objects.requireNonNull(test.get()).info(MarkupHelper.createLabel("STEP: " + stepDescription, ExtentColor.BLUE));
        LoggerUtil.logStep(stepDescription);
    }

    /**
     * Log info in the current test
     *
     * @param message Message to log
     */
    public static void logInfo(String message) {
        Objects.requireNonNull(test.get()).info(message);
        LoggerUtil.info(message);
    }

    /**
     * Log warning in the current test
     *
     * @param message Message to log
     */
    public static void logWarning(String message) {
        Objects.requireNonNull(test.get()).warning(message);
        LoggerUtil.warn(message);
    }

    /**
     * Log error in the current test
     *
     * @param message Message to log
     */
    public static void logError(String message) {
        Objects.requireNonNull(test.get()).error(message);
        LoggerUtil.error(message);
    }

    /**
     * Take screenshot and attach to the current test
     *
     * @param driver WebDriver instance
     * @param title Title of the screenshot
     */
    public static void takeScreenshot(WebDriver driver, String title) {
        if (driver != null) {
            try {
                String base64Screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.BASE64);
                Objects.requireNonNull(test.get()).info(title,
                        MediaEntityBuilder.createScreenCaptureFromBase64String(base64Screenshot).build());
                LoggerUtil.info("Screenshot captured: " + title);
            } catch (Exception e) {
                LoggerUtil.error("Failed to capture screenshot: " + title, e);
            }
        }
    }
}
