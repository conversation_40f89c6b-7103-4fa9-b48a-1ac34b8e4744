package mto.ltc.eo.util;

import com.aventstack.chaintest.ChainTest;
import com.aventstack.chaintest.model.Media;
import com.aventstack.chaintest.model.MediaType;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;

import java.util.Base64;

/**
 * Utility class for ChainTest reporting
 */
public class ChainTestManager {

    /**
     * Take a screenshot and attach it to the current test
     * 
     * @param driver WebDriver instance
     * @param title Title of the screenshot
     */
    public static void takeScreenshot(WebDriver driver, String title) {
        if (driver == null) {
            return;
        }
        
        try {
            TakesScreenshot ts = (TakesScreenshot) driver;
            String base64Screenshot = ts.getScreenshotAs(OutputType.BASE64);
            
            Media media = Media.builder()
                    .title(title)
                    .type(MediaType.IMG)
                    .base64(base64Screenshot)
                    .build();
            
            ChainTest.getCurrentTest().addMedia(media);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Log a step in the current test
     * 
     * @param stepDescription Description of the step
     */
    public static void logStep(String stepDescription) {
        ChainTest.getCurrentTest().log(stepDescription);
    }
    
    /**
     * Log an info message in the current test
     * 
     * @param message Message to log
     */
    public static void logInfo(String message) {
        ChainTest.getCurrentTest().info(message);
    }
    
    /**
     * Log a warning message in the current test
     * 
     * @param message Message to log
     */
    public static void logWarning(String message) {
        ChainTest.getCurrentTest().warning(message);
    }
    
    /**
     * Log an error message in the current test
     * 
     * @param message Message to log
     */
    public static void logError(String message) {
        ChainTest.getCurrentTest().error(message);
    }
    
    /**
     * Attach a base64 image to the current test
     * 
     * @param base64Image Base64 encoded image
     * @param title Title of the image
     */
    public static void attachImage(String base64Image, String title) {
        Media media = Media.builder()
                .title(title)
                .type(MediaType.IMG)
                .base64(base64Image)
                .build();
        
        ChainTest.getCurrentTest().addMedia(media);
    }
}
