package mto.ltc.eo.util;

import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.MediaEntityBuilder;
import com.aventstack.extentreports.Status;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;

/**
 * Utility class for ChainTest and ExtentReports integration
 */
public class ChainTestManager {
    
    private static ThreadLocal<ExtentTest> extentTest = new ThreadLocal<>();
    
    /**
     * Set the current ExtentTest instance
     * 
     * @param test ExtentTest instance
     */
    public static void setTest(ExtentTest test) {
        extentTest.set(test);
    }
    
    /**
     * Get the current ExtentTest instance
     * 
     * @return ExtentTest instance
     */
    public static ExtentTest getTest() {
        return extentTest.get();
    }
    
    /**
     * Remove the current ExtentTest instance
     */
    public static void removeTest() {
        extentTest.remove();
    }
    
    /**
     * Log a step in the current test
     * 
     * @param stepDescription Description of the step
     */
    public static void logStep(String stepDescription) {
        if (getTest() != null) {
            getTest().info("STEP: " + stepDescription);
        }
        LoggerUtil.logStep(stepDescription);
    }
    
    /**
     * Log an info message in the current test
     * 
     * @param message Message to log
     */
    public static void logInfo(String message) {
        if (getTest() != null) {
            getTest().info(message);
        }
        LoggerUtil.info(message);
    }
    
    /**
     * Log a warning message in the current test
     * 
     * @param message Message to log
     */
    public static void logWarning(String message) {
        if (getTest() != null) {
            getTest().warning(message);
        }
        LoggerUtil.warn(message);
    }
    
    /**
     * Log an error message in the current test
     * 
     * @param message Message to log
     */
    public static void logError(String message) {
        if (getTest() != null) {
            getTest().error(message);
        }
        LoggerUtil.error(message);
    }
    
    /**
     * Log an error message with exception in the current test
     * 
     * @param message Message to log
     * @param throwable Exception to log
     */
    public static void logError(String message, Throwable throwable) {
        if (getTest() != null) {
            getTest().error(message);
            getTest().error(throwable);
        }
        LoggerUtil.error(message, throwable);
    }
    
    /**
     * Take screenshot and attach to the current test
     * 
     * @param driver WebDriver instance
     * @param title Title of the screenshot
     */
    public static void takeScreenshot(WebDriver driver, String title) {
        if (driver != null) {
            try {
                String base64Screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.BASE64);
                
                if (getTest() != null) {
                    getTest().info(title, 
                            MediaEntityBuilder.createScreenCaptureFromBase64String(base64Screenshot).build());
                }
                
                LoggerUtil.info("Screenshot captured: " + title);
            } catch (Exception e) {
                LoggerUtil.error("Failed to capture screenshot: " + title, e);
            }
        }
    }
    
    /**
     * Log test pass
     * 
     * @param message Pass message
     */
    public static void logPass(String message) {
        if (getTest() != null) {
            getTest().pass(message);
        }
        LoggerUtil.info("PASS: " + message);
    }
    
    /**
     * Log test fail
     * 
     * @param message Fail message
     */
    public static void logFail(String message) {
        if (getTest() != null) {
            getTest().fail(message);
        }
        LoggerUtil.error("FAIL: " + message);
    }
    
    /**
     * Log test skip
     * 
     * @param message Skip message
     */
    public static void logSkip(String message) {
        if (getTest() != null) {
            getTest().skip(message);
        }
        LoggerUtil.warn("SKIP: " + message);
    }
    
    /**
     * Log browser action
     * 
     * @param action Browser action performed
     */
    public static void logBrowserAction(String action) {
        logInfo("Browser Action: " + action);
        LoggerUtil.logBrowserAction(action);
    }
    
    /**
     * Log element interaction
     * 
     * @param element Element being interacted with
     * @param action Action performed on the element
     */
    public static void logElementInteraction(String element, String action) {
        logInfo("Element Interaction: " + action + " on " + element);
        LoggerUtil.logElementInteraction(element, action);
    }
}
