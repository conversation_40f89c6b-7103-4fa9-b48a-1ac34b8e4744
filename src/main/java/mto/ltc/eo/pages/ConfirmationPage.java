package com.qa.eo.pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

/**
 * Page class for Confirmation page (Step 3 of 3)
 */
public class ConfirmationPage extends BasePage {

    // Page locators
    private By pageHeader = By.xpath("//h2[contains(text(),'Confirm and Submit')]");
    private By locationDetails = By.xpath("//div[contains(@class,'location-details')]");
    private By confirmAndSubmitButton = By.xpath("//button[contains(text(),'Confirm and Submit')]");
    private By backButton = By.xpath("//button[contains(text(),'Back')]");
    
    public ConfirmationPage(WebDriver driver) {
        super(driver);
    }
    
    /**
     * Check if page header is displayed
     * 
     * @return true if page header is displayed, false otherwise
     */
    public boolean isPageHeaderDisplayed() {
        return elementUtil.doIsDisplayed(pageHeader);
    }
    
    /**
     * Get page header text
     * 
     * @return Page header text
     */
    public String getPageHeaderText() {
        return elementUtil.doGetText(pageHeader);
    }
    
    /**
     * Check if location details are displayed
     * 
     * @return true if location details are displayed, false otherwise
     */
    public boolean isLocationDetailsDisplayed() {
        return elementUtil.doIsDisplayed(locationDetails);
    }
    
    /**
     * Get location details text
     * 
     * @return Location details text
     */
    public String getLocationDetailsText() {
        return elementUtil.doGetText(locationDetails);
    }
    
    /**
     * Click confirm and submit button
     * 
     * @return SuccessPage
     */
    public SuccessPage clickConfirmAndSubmitButton() {
        elementUtil.doClick(confirmAndSubmitButton);
        return new SuccessPage(driver);
    }
    
    /**
     * Click back button
     * 
     * @return LocationSelectionPage
     */
    public LocationSelectionPage clickBackButton() {
        elementUtil.doClick(backButton);
        return new LocationSelectionPage(driver);
    }
}
