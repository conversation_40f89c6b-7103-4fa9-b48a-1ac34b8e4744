package mto.ltc.eo.pages;

import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

/**
 * Page class for Location Selection page (Step 2 of 3)
 */
public class LocationSelectionPage extends BasePage {

    // Page locators
    private By pageHeader = By.xpath("//h2[contains(text(),'Select a location')]");
    private By locationItems = By.xpath("//div[contains(@class,'location-item')]");
    private By locationRadioButtons = By.xpath("//input[@type='radio']");
    private By continueButton = By.xpath("//button[contains(text(),'Continue')]");
    private By backButton = By.xpath("//button[contains(text(),'Back')]");
    
    public LocationSelectionPage(WebDriver driver) {
        super(driver);
    }
    
    /**
     * Check if page header is displayed
     * 
     * @return true if page header is displayed, false otherwise
     */
    public boolean isPageHeaderDisplayed() {
        return elementUtil.doIsDisplayed(pageHeader);
    }
    
    /**
     * Get page header text
     * 
     * @return Page header text
     */
    public String getPageHeaderText() {
        return elementUtil.doGetText(pageHeader);
    }
    
    /**
     * Get number of available locations
     * 
     * @return Number of available locations
     */
    public int getNumberOfLocations() {
        return elementUtil.getElements(locationItems).size();
    }
    
    /**
     * Select location by index
     * 
     * @param index Index of location to select (0-based)
     */
    public void selectLocationByIndex(int index) {
        List<WebElement> radioButtons = elementUtil.getElements(locationRadioButtons);
        if (index >= 0 && index < radioButtons.size()) {
            radioButtons.get(index).click();
        } else {
            System.out.println("Invalid location index: " + index);
        }
    }
    
    /**
     * Click continue button
     * 
     * @return ConfirmationPage
     */
    public ConfirmationPage clickContinueButton() {
        elementUtil.doClick(continueButton);
        return new ConfirmationPage(driver);
    }
    
    /**
     * Click back button
     * 
     * @return ClientInfoPage
     */
    public ClientInfoPage clickBackButton() {
        elementUtil.doClick(backButton);
        return new ClientInfoPage(driver);
    }
    
    /**
     * Select location by index and continue
     * 
     * @param index Index of location to select (0-based)
     * @return ConfirmationPage
     */
    public ConfirmationPage selectLocationAndContinue(int index) {
        selectLocationByIndex(index);
        return clickContinueButton();
    }
}
