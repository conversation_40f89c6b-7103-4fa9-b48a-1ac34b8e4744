package mto.ltc.eo.pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

/**
 * Page class for Client Information page (Step 1 of 3)
 */
public class ClientInfoPage extends BasePage {

    // Page locators
    private By esiProgramText = By.xpath("(//div[@class='ontario-padding-top-24-!'])[1]");
    private By clickHereLink = By.xpath("//a[contains(text(),'click here')]");
    
    // Form fields
    private By firstNameInput = By.id("INFO01");
    private By middleNameInput = By.id("INFO02");
    private By lastNameInput = By.id("INFO03");
    private By preferredFirstNameInput = By.id("INFO04");
    private By preferredLastNameInput = By.id("INFO06");

    private By streetNumberInput = By.id("INFO07");
    private By addressInput = By.id("INFO08");
    private By unitNumberInput = By.id("INFO09");
    private By poBoxNumberInput = By.id("INFO10");
    private By cityInput = By.id("city");
    private By postalCodeInput = By.id("postalCode");
    
    private By emailInput = By.id("email");
    private By phoneInput = By.id("phone");
    
    private By preferredLanguageDropdown = By.id("preferredLanguage");
    
    private By continueButton = By.xpath("//button[contains(text(),'Continue')]");
    
    public ClientInfoPage(WebDriver driver) {
        super(driver);
    }
    
    /**
     * Check if ESI program text is displayed
     * 
     * @return true if ESI program text is displayed, false otherwise
     */
    public boolean isESIProgramTextDisplayed() {
        return elementUtil.doIsDisplayed(esiProgramText);
    }
    
    /**
     * Get ESI program text
     * 
     * @return ESI program text
     */
    public String getESIProgramText() {
        return elementUtil.doGetText(esiProgramText);
    }
    
    /**
     * Check if "click here" link is displayed
     * 
     * @return true if "click here" link is displayed, false otherwise
     */
    public boolean isClickHereLinkDisplayed() {
        return elementUtil.doIsDisplayed(clickHereLink);
    }
    
    /**
     * Click on "click here" link
     */
    public void clickOnClickHereLink() {
        elementUtil.doClick(clickHereLink);
    }
    
    /**
     * Fill client information form
     * 
     * @param firstName First name
     * @param middleName Middle name
     * @param lastName Last name
     * @param preferredFirstName Preferred first name
     * @param preferredLastName Preferred last name
     * @param address Address
     * @param city City
     * @param postalCode Postal code
     * @param email Email
     * @param phone Phone
     * @param preferredLanguage Preferred language
     * @return LocationSelectionPage
     */
    public LocationSelectionPage fillClientInfoForm(String firstName, String middleName, String lastName,
                                                  String preferredFirstName, String preferredLastName,
                                                  String address, String city, String postalCode,
                                                  String email, String phone, String preferredLanguage) {
        
        elementUtil.doSendKeys(firstNameInput, firstName);
        
        if (middleName != null && !middleName.isEmpty()) {
            elementUtil.doSendKeys(middleNameInput, middleName);
        }
        
        elementUtil.doSendKeys(lastNameInput, lastName);
        
        if (preferredFirstName != null && !preferredFirstName.isEmpty()) {
            elementUtil.doSendKeys(preferredFirstNameInput, preferredFirstName);
        }
        
        if (preferredLastName != null && !preferredLastName.isEmpty()) {
            elementUtil.doSendKeys(preferredLastNameInput, preferredLastName);
        }
        
        elementUtil.doSendKeys(addressInput, address);
        elementUtil.doSendKeys(cityInput, city);
        elementUtil.doSendKeys(postalCodeInput, postalCode);
        
        elementUtil.doSendKeys(emailInput, email);
        elementUtil.doSendKeys(phoneInput, phone);
        
        elementUtil.doSelectDropdownByVisibleText(preferredLanguageDropdown, preferredLanguage);
        
        elementUtil.doClick(continueButton);
        
        return new LocationSelectionPage(driver);
    }
    
    /**
     * Click continue button
     * 
     * @return LocationSelectionPage
     */
    public LocationSelectionPage clickContinueButton() {
        elementUtil.doClick(continueButton);
        return new LocationSelectionPage(driver);
    }
}
