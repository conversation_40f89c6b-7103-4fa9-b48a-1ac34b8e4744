package mto.ltc.eo.pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import mto.ltc.eo.util.ElementUtil;

/**
 * Base page class with common methods for all pages
 */
public class BasePage {

    protected WebDriver driver;
    protected ElementUtil elementUtil;
    
    // Common locators for all pages
    protected By ontarioLogo = By.xpath("//img[contains(@src, 'ontario-logo')]");
    protected By languageToggle = By.xpath("//a[@class='language-toggle']");
    protected By ministryHeader = By.xpath("//div[contains(@class,'ontario-header__service-name')]");
    protected By findCounsellorHeader = By.xpath("//h1[contains(text(),'Find an Employment Counsellor')]");
    
    public BasePage(WebDriver driver) {
        this.driver = driver;
        this.elementUtil = new ElementUtil(driver);
    }
    
    /**
     * Get the page title
     * 
     * @return Page title
     */
    public String getPageTitle() {
        return elementUtil.getPageTitle();
    }
    
    /**
     * Get the page URL
     * 
     * @return Page URL
     */
    public String getPageURL() {
        return elementUtil.getPageURL();
    }
    
    /**
     * Check if Ontario logo is displayed
     * 
     * @return true if logo is displayed, false otherwise
     */
    public boolean isOntarioLogoDisplayed() {
        return elementUtil.doIsDisplayed(ontarioLogo);
    }
    
    /**
     * Get the language toggle text
     * 
     * @return Language toggle text
     */
    public String getLanguageToggleText() {
        return elementUtil.doGetText(languageToggle);
    }
    
    /**
     * Check if language toggle is displayed
     * 
     * @return true if language toggle is displayed, false otherwise
     */
    public boolean isLanguageToggleDisplayed() {
        return elementUtil.doIsDisplayed(languageToggle);
    }
    
    /**
     * Get the ministry header text
     * 
     * @return Ministry header text
     */
    public String getMinistryHeaderText() {
        return elementUtil.doGetText(ministryHeader);
    }
    
    /**
     * Check if ministry header is displayed
     * 
     * @return true if ministry header is displayed, false otherwise
     */
    public boolean isMinistryHeaderDisplayed() {
        return elementUtil.doIsDisplayed(ministryHeader);
    }
    
    /**
     * Get the "Find an Employment Counsellor" header text
     * 
     * @return Find Counsellor header text
     */
    public String getFindCounsellorHeaderText() {
        return elementUtil.doGetText(findCounsellorHeader);
    }
    
    /**
     * Check if "Find an Employment Counsellor" header is displayed
     * 
     * @return true if Find Counsellor header is displayed, false otherwise
     */
    public boolean isFindCounsellorHeaderDisplayed() {
        return elementUtil.doIsDisplayed(findCounsellorHeader);
    }
    
    /**
     * Wait for page title
     * 
     * @param title Title to wait for
     * @param timeOut Timeout in seconds
     * @return true if title is found, false otherwise
     */
    public boolean waitForPageTitle(String title, int timeOut) {
        return elementUtil.waitForTitleIs(title, timeOut);
    }
}
